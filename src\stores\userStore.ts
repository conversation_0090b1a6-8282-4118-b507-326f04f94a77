import { create } from 'zustand';
import type { Role, Permission } from '../types/api';

interface RoleOperationState {
    isCreating: boolean;
    isUpdating: Record<number, boolean>;
    isDeleting: Record<number, boolean>;
}

interface UserState {
    // Data
    roles: Role[];
    customRoles: Role[];
    permissions: Permission[];

    // Global loading state
    isLoading: boolean;
    error: string | null;

    // Pagination state for roles
    rolesPagination: {
        page: number;
        pageSize: number;
        totalPages: number;
        totalItems: number;
    };

    // Operation states
    roleOperations: RoleOperationState;

    // Actions
    setRoles: (roles: Role[], customRoles: Role[]) => void;
    setPermissions: (permissions: Permission[]) => void;
    setLoading: (isLoading: boolean) => void;
    setError: (error: string | null) => void;

    // Role pagination actions
    setRolesPagination: (pagination: Partial<UserState['rolesPagination']>) => void;

    // Custom role actions
    addCustomRole: (role: Role) => void;
    updateCustomRole: (role: Role) => void;
    removeCustomRole: (roleId: number) => void;

    // Operation state actions
    setRoleCreating: (isCreating: boolean) => void;
    setRoleUpdating: (roleId: number, isUpdating: boolean) => void;
    setRoleDeleting: (roleId: number, isDeleting: boolean) => void;
}

export const useUserStore = create<UserState>((set) => ({
    // Initial data
    roles: [],
    customRoles: [],
    permissions: [],

    // Initial loading state
    isLoading: false,
    error: null,

    // Initial pagination state
    rolesPagination: {
        page: 1,
        pageSize: 10,
        totalPages: 1,
        totalItems: 0,
    },

    // Initial operation states
    roleOperations: {
        isCreating: false,
        isUpdating: {},
        isDeleting: {},
    },

    // Data setters
    setRoles: (roles, customRoles) => set({ roles, customRoles }),
    setPermissions: (permissions) => set({ permissions }),
    setLoading: (isLoading) => set({ isLoading }),
    setError: (error) => set({ error }),

    // Pagination setters
    setRolesPagination: (pagination) => set((state) => ({
        rolesPagination: { ...state.rolesPagination, ...pagination }
    })),

    // Custom role actions with optimistic updates
    addCustomRole: (role) => set((state) => ({
        customRoles: [...state.customRoles, role]
    })),

    updateCustomRole: (role) => set((state) => ({
        customRoles: state.customRoles.map((r) => (r.id === role.id ? role : r))
    })),

    removeCustomRole: (roleId) => set((state) => ({
        customRoles: state.customRoles.filter((r) => r.id !== roleId)
    })),

    // Operation state setters
    setRoleCreating: (isCreating) => set((state) => ({
        roleOperations: { ...state.roleOperations, isCreating }
    })),

    setRoleUpdating: (roleId, isUpdating) => set((state) => ({
        roleOperations: {
            ...state.roleOperations,
            isUpdating: {
                ...state.roleOperations.isUpdating,
                [roleId]: isUpdating
            }
        }
    })),

    setRoleDeleting: (roleId, isDeleting) => set((state) => ({
        roleOperations: {
            ...state.roleOperations,
            isDeleting: {
                ...state.roleOperations.isDeleting,
                [roleId]: isDeleting
            }
        }
    })),
}));