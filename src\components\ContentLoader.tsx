import React from "react";
import { Shield } from "lucide-react";
import { Skeleton } from "./ui/Skeleton";
import { Card, CardContent } from "./ui/Card";
import { motion } from "framer-motion";

interface ContentLoaderProps {
  /**
   * The type of content being loaded
   */
  type?: "card" | "table" | "list" | "detail";
  
  /**
   * Optional message to display
   */
  message?: string;
  
  /**
   * Number of skeleton items to render (for list/table types)
   */
  count?: number;
  
  /**
   * Whether to show a partial view of the content (for better perceived performance)
   */
  partial?: boolean;
  
  /**
   * Optional className to apply to the container
   */
  className?: string;
}

/**
 * A component that displays appropriate skeleton loaders based on content type
 */
const ContentLoader: React.FC<ContentLoaderProps> = ({
  type = "card",
  message = "Loading...",
  count = 3,
  partial = false,
  className = "",
}) => {
  // Render card skeletons (for dashboard cards, etc.)
  const renderCardSkeletons = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {Array.from({ length: count }).map((_, i) => (
        <motion.div
          key={i}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: i * 0.1 }}
        >
          <Card className="border border-dark-700">
            <CardContent className="p-6 space-y-4">
              <div className="flex items-center space-x-3">
                <Skeleton className="h-10 w-10 rounded-md" />
                <Skeleton className="h-6 w-32" />
              </div>
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <div className="flex justify-between pt-4">
                <Skeleton className="h-9 w-20" />
                <Skeleton className="h-9 w-20" />
              </div>
            </CardContent>
          </Card>
        </motion.div>
      ))}
    </div>
  );

  // Render table skeleton
  const renderTableSkeleton = () => (
    <div className="border border-dark-700 rounded-lg overflow-hidden">
      {/* Table header */}
      <div className="bg-dark-800 p-4">
        <div className="flex justify-between items-center">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-8 w-32" />
        </div>
      </div>
      
      {/* Table rows */}
      <div className="divide-y divide-dark-700">
        {Array.from({ length: count }).map((_, i) => (
          <motion.div
            key={i}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3, delay: i * 0.05 }}
            className="p-4"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div>
                  <Skeleton className="h-5 w-32 mb-1" />
                  <Skeleton className="h-4 w-48" />
                </div>
              </div>
              <div className="flex space-x-2">
                <Skeleton className="h-8 w-24" />
                <Skeleton className="h-8 w-8 rounded-md" />
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );

  // Render list skeleton
  const renderListSkeleton = () => (
    <div className="space-y-4">
      {Array.from({ length: count }).map((_, i) => (
        <motion.div
          key={i}
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: i * 0.05 }}
          className="flex items-center p-4 border border-dark-700 rounded-lg"
        >
          <Skeleton className="h-10 w-10 rounded-md mr-4" />
          <div className="flex-1">
            <Skeleton className="h-5 w-48 mb-2" />
            <Skeleton className="h-4 w-full" />
          </div>
          <Skeleton className="h-8 w-24" />
        </motion.div>
      ))}
    </div>
  );

  // Render detail view skeleton
  const renderDetailSkeleton = () => (
    <div className="space-y-8">
      <div className="flex items-center gap-4">
        <Skeleton className="h-12 w-12 rounded-lg" />
        <div>
          <Skeleton className="h-8 w-64 mb-2" />
          <Skeleton className="h-5 w-96" />
        </div>
      </div>

      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i}>
                <Skeleton className="h-5 w-24 mb-2" />
                <Skeleton className="h-6 w-32" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6 space-y-6">
          <Skeleton className="h-7 w-48" />
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <Skeleton key={i} className="h-16 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  // If partial is true, render a minimal loading indicator with the content
  if (partial) {
    return (
      <div className={`relative ${className}`}>
        <div className="absolute top-0 left-0 right-0 flex justify-center">
          <div className="bg-dark-900 text-primary-500 px-4 py-2 rounded-b-lg shadow-lg flex items-center">
            <div className="animate-spin mr-2 h-4 w-4 border-2 border-primary-500 border-t-transparent rounded-full" />
            <span className="text-sm">{message}</span>
          </div>
        </div>
        <div className="opacity-60">
          {type === "card" && renderCardSkeletons()}
          {type === "table" && renderTableSkeleton()}
          {type === "list" && renderListSkeleton()}
          {type === "detail" && renderDetailSkeleton()}
        </div>
      </div>
    );
  }

  // Otherwise, render a centered loading indicator with skeletons
  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex flex-col items-center justify-center py-6">
        <div className="p-4 rounded-full bg-dark-800 mb-4 animate-pulse">
          <Shield size={32} className="text-dark-400" />
        </div>
        <h3 className="text-lg font-medium text-dark-300 mb-2">{message}</h3>
      </div>
      
      {type === "card" && renderCardSkeletons()}
      {type === "table" && renderTableSkeleton()}
      {type === "list" && renderListSkeleton()}
      {type === "detail" && renderDetailSkeleton()}
    </div>
  );
};

export default ContentLoader;
