<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />

    <!-- Security Meta Tags -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff" />
    <meta http-equiv="X-Frame-Options" content="DENY" />
    <meta http-equiv="X-XSS-Protection" content="1; mode=block" />
    <meta name="referrer" content="strict-origin-when-cross-origin" />

    <!-- Viewport and Mobile Optimization -->
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, viewport-fit=cover"
    />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta
      name="apple-mobile-web-app-status-bar-style"
      content="black-translucent"
    />
    <meta name="apple-mobile-web-app-title" content="CloudAudit" />

    <!-- Theme and Color -->
    <meta name="theme-color" content="#3B82F6" />
    <meta name="msapplication-TileColor" content="#3B82F6" />
    <meta name="msapplication-navbutton-color" content="#3B82F6" />

    <!-- Icons and Manifest -->
    <link rel="icon" type="image/svg+xml" href="/logo.svg" />
    <link rel="apple-touch-icon" href="/icons/icon-192x192.png" />
    <link rel="manifest" href="/manifest.json" />

    <!-- SEO Meta Tags -->
    <title>CloudAudit - Cloud Security Scanner</title>
    <meta
      name="description"
      content="Comprehensive cloud security scanning and compliance monitoring platform for AWS, Azure, and GCP environments."
    />
    <meta
      name="keywords"
      content="cloud security, compliance, AWS security, Azure security, GCP security, security scanning, vulnerability assessment"
    />
    <meta name="author" content="CloudAudit Team" />

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="CloudAudit - Cloud Security Scanner" />
    <meta
      property="og:description"
      content="Comprehensive cloud security scanning and compliance monitoring platform"
    />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://cloudaudit.com" />
    <meta property="og:image" content="https://cloudaudit.com/og-image.png" />
    <meta property="og:site_name" content="CloudAudit" />

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="CloudAudit - Cloud Security Scanner" />
    <meta
      name="twitter:description"
      content="Comprehensive cloud security scanning and compliance monitoring platform"
    />
    <meta
      name="twitter:image"
      content="https://cloudaudit.com/twitter-image.png"
    />

    <!-- DNS Prefetch and Preconnect -->
    <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
    <link rel="dns-prefetch" href="https://fonts.gstatic.com" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

    <!-- Font Loading with Performance Optimization -->
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
      rel="stylesheet"
      media="print"
      onload="this.media='all'"
    />
    <noscript>
      <link
        href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
        rel="stylesheet"
      />
    </noscript>

    <!-- Preload Critical Resources -->
    <link rel="modulepreload" href="/src/main.tsx" />

    <!-- Critical CSS Inline (if needed) -->
    <style>
      /* Critical CSS for initial render */
      body {
        margin: 0;
        padding: 0;
        font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI",
          "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans",
          "Helvetica Neue", sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background-color: #020617;
        color: #f1f5f9;
      }

      #root {
        min-height: 100vh;
      }

      /* Loading spinner for initial load */
      .loading-spinner {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 40px;
        height: 40px;
        border: 4px solid #334155;
        border-top: 4px solid #3b82f6;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% {
          transform: translate(-50%, -50%) rotate(0deg);
        }
        100% {
          transform: translate(-50%, -50%) rotate(360deg);
        }
      }
    </style>
  </head>
  <body class="bg-dark-950 text-dark-100">
    <!-- Loading Fallback -->
    <div id="root">
      <div class="loading-spinner"></div>
    </div>

    <!-- Main Application Script -->
    <script type="module" src="/src/main.tsx"></script>

    <!-- Fallback for browsers without module support -->
    <script nomodule>
      document.body.innerHTML =
        '<div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;"><h1>Browser Not Supported</h1><p>Please use a modern browser to access CloudAudit.</p></div>';
    </script>
  </body>
</html>
