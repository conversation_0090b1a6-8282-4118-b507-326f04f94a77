{"version": 2, "name": "cloudaudit-frontend", "builds": [{"src": "package.json", "use": "@vercel/static-build", "config": {"distDir": "dist"}}], "routes": [{"src": "/assets/(.*)", "headers": {"Cache-Control": "public, max-age=31536000, immutable"}}, {"src": "/logo.svg", "headers": {"Cache-Control": "public, max-age=604800"}}, {"src": "/manifest.json", "headers": {"Cache-Control": "public, max-age=86400"}}, {"src": "/robots.txt", "headers": {"Cache-Control": "public, max-age=86400"}}, {"src": "/(.*)", "dest": "/index.html", "headers": {"X-Content-Type-Options": "nosniff", "X-Frame-Options": "DENY", "X-XSS-Protection": "1; mode=block", "Referrer-Policy": "strict-origin-when-cross-origin", "Permissions-Policy": "camera=(), microphone=(), geolocation=(), payment=()", "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload"}}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}], "env": {"VITE_APP_ENV": "production", "VITE_BUILD_TARGET": "production"}, "build": {"env": {"VITE_APP_ENV": "production", "VITE_BUILD_TARGET": "production", "VITE_COMMIT_HASH": "$VERCEL_GIT_COMMIT_SHA"}}, "functions": {"app/api/**/*.js": {"runtime": "nodejs18.x"}}, "regions": ["iad1"], "github": {"silent": true}}