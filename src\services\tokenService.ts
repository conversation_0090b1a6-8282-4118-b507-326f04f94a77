import axios from 'axios';
import { clearTokens, getRefreshToken, setTokens, createBase<PERSON><PERSON>, ServiceError, ServiceErrorType } from './baseService';
import type { AuthResponse } from '../types/api';

// A single promise for token refresh to prevent multiple refresh calls
let tokenRefreshPromise: Promise<string> | null = null;

/**
 * Refresh the access token using the refresh token
 * This function is separated from auth.ts to avoid circular dependencies
 * when imported by baseService.ts
 */
export const refreshToken = async (): Promise<string> => {
    // If a refresh is already in progress, return the existing promise
    if (tokenRefreshPromise) {
        return tokenRefreshPromise;
    }

    // Create a new refresh token promise
    tokenRefreshPromise = (async () => {
        try {
            // Get the refresh token from storage
            const refreshTokenValue = getRefreshToken();
            if (!refreshTokenValue) {
                throw new ServiceError(
                    ServiceErrorType.REFRESH_FAILED,
                    'No refresh token available'
                );
            }

            // Create a separate API instance for token refresh to avoid interceptor loops
            const tokenAPI = createBaseApi();

            // Make the refresh token request
            const response = await tokenAPI.post<AuthResponse>('/api/refresh-token', {
                refresh_token: refreshTokenValue
            });

            // Validate the response
            if (!response.data.access_token || !response.data.refresh_token) {
                throw new ServiceError(
                    ServiceErrorType.REFRESH_FAILED,
                    'Invalid token response from server'
                );
            }

            // Store the new tokens
            setTokens(response.data.access_token, response.data.refresh_token);

            // Return the new access token
            return response.data.access_token;
        } catch (error) {
            // Handle specific error cases
            if (error instanceof ServiceError) {
                // If it's already a ServiceError, just rethrow it
                clearTokens();
                throw error;
            } else if (axios.isAxiosError(error)) {
                // Handle network or server errors
                const axiosError = error;
                const status = axiosError.response?.status;

                if (status === 401 || status === 403) {
                    clearTokens();
                    throw new ServiceError(
                        ServiceErrorType.UNAUTHORIZED,
                        'Your session has expired. Please sign in again.',
                        error,
                        status
                    );
                } else {
                    clearTokens();
                    throw new ServiceError(
                        ServiceErrorType.REFRESH_FAILED,
                        'Failed to refresh authentication. Please sign in again.',
                        error,
                        status
                    );
                }
            } else {
                // Handle unknown errors
                clearTokens();
                throw new ServiceError(
                    ServiceErrorType.UNKNOWN_ERROR,
                    'An unexpected error occurred while refreshing authentication.',
                    error
                );
            }
        } finally {
            // Always clear the promise when done
            tokenRefreshPromise = null;
        }
    })();

    return tokenRefreshPromise;
};
