import { ServiceError } from '@/services/baseService';
import { createScan, getAccountScans, getFindingDetail, getFindings, getRegions, getScanDetails, getScans, remediateFinding } from '@/services/scan';
import type { CreateScanRequest } from '@/types/api';
import { useInfiniteQuery, useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { QUERY_KEYS as ACCOUNT_QUERY_KEYS } from '@/hooks/useAccounts';

// Define query keys as constants for better maintainability
export const QUERY_KEYS = {
    SCANS: 'scans',
    INFINITE_SCANS: 'infiniteScans',
    ACCOUNT_SCANS: 'accountScans',
    INFINITE_ACCOUNT_SCANS: 'infiniteAccountScans',
    SCAN_DETAILS: 'scanDetails',
    FINDINGS: 'findings',
    INFINITE_FINDINGS: 'infiniteFindings',
    FINDING_DETAIL: 'findingDetail',
    FINDING_REMEDIATE: 'findingRemediate',
    SERVICES: 'services',
    REGIONS: 'regions',
    ACCOUNTS: 'accounts',
};

/**
 * Hook to get all scans with pagination (traditional pagination)
 */
export const useScans = (page: number = 1, pageSize: number = 20) => {
    return useQuery({
        queryKey: [QUERY_KEYS.SCANS, page, pageSize],
        queryFn: () => getScans(page, pageSize),
        staleTime: 1 * 60 * 1000, // 1 minute
        retry: (failureCount, error) => {
            // Don't retry on 4xx errors
            if (error instanceof ServiceError && error.statusCode && error.statusCode >= 400 && error.statusCode < 500) {
                return false;
            }
            return failureCount < 3;
        }
    });
};

/**
 * Hook to get all scans with infinite pagination
 * This allows for "Load More" or infinite scroll functionality
 *
 * @param pageSize The page size (default: 20)
 * @param enablePolling Whether to enable automatic polling for in-progress scans (default: true)
 * @param pollingInterval The polling interval in milliseconds (default: 15000 - 15 seconds)
 */
export const useInfiniteScans = (
    pageSize: number = 20,
    enablePolling: boolean = true,
    pollingInterval: number = 15000
) => {
    const query = useInfiniteQuery({
        queryKey: [QUERY_KEYS.INFINITE_SCANS, pageSize],
        queryFn: ({ pageParam = 1 }) => getScans(pageParam, pageSize),
        initialPageParam: 1,
        getNextPageParam: (lastPage) => {
            // If we're on the last page, return undefined to indicate there are no more pages
            if (lastPage.pagination.page >= lastPage.pagination.total_pages) {
                return undefined;
            }
            // Otherwise, return the next page number
            return lastPage.pagination.page + 1;
        },
        getPreviousPageParam: (firstPage) => {
            // If we're on the first page, return undefined to indicate there are no previous pages
            if (firstPage.pagination.page <= 1) {
                return undefined;
            }
            // Otherwise, return the previous page number
            return firstPage.pagination.page - 1;
        },
        staleTime: 1000, // 1 second
        refetchInterval: enablePolling ? pollingInterval : false,
        retry: (failureCount, error) => {
            // Don't retry on 4xx errors
            if (error instanceof ServiceError && error.statusCode && error.statusCode >= 400 && error.statusCode < 500) {
                return false;
            }
            return failureCount < 3;
        }
    });

    return {
        ...query,
        // Add a timestamp for when the data was last updated
        lastUpdated: query.dataUpdatedAt
    };
};

/**
 * Hook to get scans for a specific account with pagination (traditional pagination)
 */
export const useAccountScans = (accountId: number | null, page: number = 1, pageSize: number = 20) => {
    return useQuery({
        queryKey: [QUERY_KEYS.ACCOUNT_SCANS, accountId, page, pageSize],
        queryFn: () => {
            if (!accountId) throw new Error('Account ID is required');
            return getAccountScans(accountId, page, pageSize);
        },
        enabled: !!accountId,
        staleTime: 1 * 60 * 1000, // 1 minute
        retry: (failureCount, error) => {
            // Don't retry on 4xx errors
            if (error instanceof ServiceError && error.statusCode && error.statusCode >= 400 && error.statusCode < 500) {
                return false;
            }
            return failureCount < 3;
        }
    });
};

/**
 * Hook to get scans for a specific account with infinite pagination
 * This allows for "Load More" or infinite scroll functionality
 */
export const useInfiniteAccountScans = (accountId: number | null, pageSize: number = 20) => {
    return useInfiniteQuery({
        queryKey: [QUERY_KEYS.INFINITE_ACCOUNT_SCANS, accountId, pageSize],
        queryFn: ({ pageParam = 1 }) => {
            if (!accountId) throw new Error('Account ID is required');
            return getAccountScans(accountId, pageParam, pageSize);
        },
        initialPageParam: 1,
        getNextPageParam: (lastPage) => {
            // If we're on the last page, return undefined to indicate there are no more pages
            if (lastPage.pagination.page >= lastPage.pagination.total_pages) {
                return undefined;
            }
            // Otherwise, return the next page number
            return lastPage.pagination.page + 1;
        },
        getPreviousPageParam: (firstPage) => {
            // If we're on the first page, return undefined to indicate there are no previous pages
            if (firstPage.pagination.page <= 1) {
                return undefined;
            }
            // Otherwise, return the previous page number
            return firstPage.pagination.page - 1;
        },
        enabled: !!accountId,
        staleTime: 1 * 60 * 1000, // 1 minute
        retry: (failureCount, error) => {
            // Don't retry on 4xx errors
            if (error instanceof ServiceError && error.statusCode && error.statusCode >= 400 && error.statusCode < 500) {
                return false;
            }
            return failureCount < 3;
        }
    });
};

/**
 * Hook to get available regions for a cloud provider
 */
export const useRegions = (cloudProviderId: number | null) => {
    return useQuery({
        queryKey: [QUERY_KEYS.REGIONS, cloudProviderId],
        queryFn: () => {
            if (!cloudProviderId) throw new Error('Cloud provider ID is required');
            return getRegions(cloudProviderId);
        },
        enabled: !!cloudProviderId,
        staleTime: 5 * 60 * 1000, // 5 minutes
        retry: (failureCount, error) => {
            // Don't retry on 4xx errors
            if (error instanceof ServiceError && error.statusCode && error.statusCode >= 400 && error.statusCode < 500) {
                return false;
            }
            return failureCount < 3;
        }
    });
};

/**
 * Hook to create a new scan
 */
export const useCreateScan = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (data: CreateScanRequest) => createScan(data),
        onSuccess: (_, variables) => {
            // Invalidate all scan queries to refetch the latest data
            queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.SCANS] });
            queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.INFINITE_SCANS] });
            queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ACCOUNT_SCANS] });
            queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.INFINITE_ACCOUNT_SCANS] });

            // Also invalidate the account details query for the specific account
            // This ensures the account details page is updated with the latest scan information
            if (variables?.account_id) {
                // Also invalidate using the ACCOUNT_QUERY_KEYS constant for consistency
                queryClient.invalidateQueries({
                    queryKey: [ACCOUNT_QUERY_KEYS.ACCOUNT_DETAILS, variables.account_id]
                });
            }

            // Show success toast
            toast.success('Scan started successfully');
        },
        onError: (error) => {
            toast.error(error instanceof ServiceError
                ? error.message
                : 'Failed to start scan. Please try again.');
        }
    });
};

/**
 * Hook to get detailed information for a specific scan
 * @param scanId The scan ID
 * @param enablePolling Whether to enable automatic polling for in-progress scans (default: true)
 * @param pollingInterval The polling interval in milliseconds (default: 10000 - 10 seconds)
 */
export const useScanDetails = (
    scanId: number | null,
    enablePolling: boolean = true,
    pollingInterval: number = 10000
) => {
    const query = useQuery({
        queryKey: [QUERY_KEYS.SCAN_DETAILS, scanId],
        queryFn: () => {
            if (!scanId) throw new Error('Scan ID is required');
            return getScanDetails(scanId);
        },
        enabled: !!scanId,
        staleTime: 0, // 1 minute
        refetchInterval: enablePolling ? pollingInterval : false,
        retry: (failureCount, error) => {
            // Don't retry on 4xx errors
            if (error instanceof ServiceError && error.statusCode && error.statusCode >= 400 && error.statusCode < 500) {
                return false;
            }
            return failureCount < 3;
        }
    });

    return {
        ...query,
        // Add a timestamp for when the data was last updated
        lastUpdated: query.dataUpdatedAt
    };
};

/**
 * Hook to get findings for a specific scan and service with infinite pagination
 * This allows for "Load More" or infinite scroll functionality
 * @param scanId The scan ID
 * @param serviceId The service ID
 * @param pageSize The page size (default: 20)
 * @param filters Optional filters for the findings (e.g., status)
 */
export const useInfiniteFindings = (
    scanId: number | null,
    serviceId: number | null,
    pageSize: number = 20,
    filters?: { status?: string[] }
) => {
    return useInfiniteQuery({
        queryKey: [QUERY_KEYS.INFINITE_FINDINGS, scanId, serviceId, pageSize, filters],
        queryFn: ({ pageParam = 1 }) => {
            if (!scanId) throw new Error('Scan ID is required');
            if (!serviceId) throw new Error('Service ID is required');
            return getFindings(scanId, serviceId, pageParam, pageSize, filters);
        },
        initialPageParam: 1,
        getNextPageParam: (lastPage) => {
            // If we're on the last page, return undefined to indicate there are no more pages
            if (lastPage.pagination.page >= lastPage.pagination.total_pages) {
                return undefined;
            }
            // Otherwise, return the next page number
            return lastPage.pagination.page + 1;
        },
        getPreviousPageParam: (firstPage) => {
            // If we're on the first page, return undefined to indicate there are no previous pages
            if (firstPage.pagination.page <= 1) {
                return undefined;
            }
            // Otherwise, return the previous page number
            return firstPage.pagination.page - 1;
        },
        enabled: !!scanId && !!serviceId,
        staleTime: 0, // 1 minute
        retry: (failureCount, error) => {
            // Don't retry on 4xx errors
            if (error instanceof ServiceError && error.statusCode && error.statusCode >= 400 && error.statusCode < 500) {
                return false;
            }
            return failureCount < 3;
        }
    });
};

/**
 * Hook to get detailed information for a specific finding
 * @param findingId The finding ID
 * @param options Additional options for the query
 */
export const useFindingDetail = (findingId: number | null, options?: { enabled?: boolean }) => {
    return useQuery({
        queryKey: [QUERY_KEYS.FINDING_DETAIL, findingId],
        queryFn: () => {
            if (!findingId) throw new Error('Finding ID is required');
            return getFindingDetail(findingId);
        },
        enabled: options?.enabled !== undefined ? options.enabled && !!findingId : !!findingId,
        staleTime: 0, // 5 minutes
        retry: (failureCount, error) => {
            // Don't retry on 4xx errors
            if (error instanceof ServiceError && error.statusCode && error.statusCode >= 400 && error.statusCode < 500) {
                return false;
            }
            return failureCount < 3;
        }
    });
};

/**
 * Hook to remediate a specific finding
 * @returns Mutation for remediating a finding
 */
export const useRemediateFinding = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationKey: [QUERY_KEYS.FINDING_REMEDIATE],
        mutationFn: (params: { findingId: number; detailData: Record<string, any> }) =>
            remediateFinding(params.findingId, params.detailData),
        onSuccess: (_, params) => {
            // Invalidate the finding detail query to refetch with updated data
            queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.FINDING_DETAIL, params.findingId] });

            // Show success toast
            toast.success('Finding remediation initiated successfully');
        },
        onError: (error) => {
            toast.error(error instanceof ServiceError
                ? error.message
                : 'Failed to remediate finding. Please try again.');
        }
    });
};
