import React from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { usePermissionContext } from '@/contexts/PermissionContext';
import { PermissionName } from '@/types/permissions';
import LoadingScreen from './LoadingScreen';

/**
 * Props for the PermissionRoute component
 */
interface PermissionRouteProps {
  /**
   * The permission(s) required to access the route
   * If an array is provided, the user must have ALL permissions to access the route
   */
  permissions: PermissionName | PermissionName[];
  
  /**
   * If true, the user must have ANY of the permissions to access the route
   * If false (default), the user must have ALL permissions
   */
  anyPermission?: boolean;
  
  /**
   * The path to redirect to if the user doesn't have the required permissions
   * Defaults to '/dashboard'
   */
  redirectTo?: string;
  
  /**
   * Optional message to display when redirecting
   */
  redirectMessage?: string;
}

/**
 * Component that protects routes based on user permissions
 * This component should be used in the router configuration
 */
const PermissionRoute: React.FC<PermissionRouteProps> = ({
  permissions,
  anyPermission = false,
  redirectTo = '/dashboard',
  redirectMessage = 'You do not have permission to access this page',
}) => {
  const location = useLocation();
  const { hasAllPermissions, hasAnyPermission, isLoading } = usePermissionContext();

  // Show loading screen while checking permissions
  if (isLoading) {
    return <LoadingScreen message="Checking permissions..." />;
  }

  // Convert single permission to array
  const permissionArray = Array.isArray(permissions) ? permissions : [permissions];

  // Check if the user has the required permissions
  const hasRequiredPermissions = anyPermission
    ? hasAnyPermission(permissionArray)
    : hasAllPermissions(permissionArray);

  // If the user doesn't have the required permissions, redirect to the specified path
  if (!hasRequiredPermissions) {
    return (
      <Navigate
        to={redirectTo}
        state={{ 
          from: location,
          message: redirectMessage,
          error: true
        }}
        replace
      />
    );
  }

  // If the user has the required permissions, render the child routes
  return <Outlet />;
};

export default PermissionRoute;
