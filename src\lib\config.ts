/**
 * Application Configuration
 * Centralized configuration management for environment variables
 */

// Type definitions for configuration
export interface AppConfig {
  // API Configuration
  api: {
    baseUrl: string;
    timeout: number;
    maxRetries: number;
  };

  // Application Configuration
  app: {
    name: string;
    version: string;
    environment: 'development' | 'staging' | 'production';
    baseUrl: string;
  };

  // Security Configuration
  security: {
    enableSecurityHeaders: boolean;
    cspMode: 'report-only' | 'enforce';
    forceHttps: boolean;
    sessionTimeout: number;
  };

  // Feature Flags
  features: {
    enableReactQueryDevtools: boolean;
    enablePerformanceMonitoring: boolean;
    enableErrorReporting: boolean;
    enableAnalytics: boolean;
  };

  // Logging Configuration
  logging: {
    level: 'debug' | 'info' | 'warn' | 'error';
    enableConsoleLogging: boolean;
    enableRemoteLogging: boolean;
    remoteLogEndpoint?: string;
  };

  // Performance Configuration
  performance: {
    enableServiceWorker: boolean;
    cacheStrategy: 'cache-first' | 'network-first' | 'stale-while-revalidate';
    cacheDuration: number;
  };

  // Third-party Services
  services: {
    sentryDsn?: string;
    gaTrackingId?: string;
    hotjarSiteId?: string;
  };

  // Build Configuration
  build: {
    target: string;
    enableSourceMaps: boolean;
    enableBundleAnalyzer: boolean;
  };

  // Development Configuration
  development: {
    port: number;
    host: string;
    enableHmr: boolean;
    enableHttps: boolean;
  };

  // Deployment Configuration
  deployment: {
    platform?: string;
    cdnBaseUrl?: string;
    assetBaseUrl?: string;
  };

  // Monitoring Configuration
  monitoring: {
    healthCheckEndpoint: string;
    metricsInterval: number;
    enableRum: boolean;
  };
}

// Helper function to parse boolean environment variables
const parseBoolean = (value: string | undefined, defaultValue: boolean = false): boolean => {
  if (value === undefined) return defaultValue;
  return value.toLowerCase() === 'true';
};

// Helper function to parse number environment variables
const parseNumber = (value: string | undefined, defaultValue: number): number => {
  if (value === undefined) return defaultValue;
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? defaultValue : parsed;
};

// Helper function to validate environment
const validateEnvironment = (env: string | undefined): 'development' | 'staging' | 'production' => {
  if (env === 'staging' || env === 'production') return env;
  return 'development';
};

// Create configuration object
export const config: AppConfig = {
  // API Configuration
  api: {
    baseUrl: import.meta.env.VITE_API_BASE_URL || 
             import.meta.env.VITE_BACKEND_LOCAL_BASE_URL || 
             'http://localhost:8000',
    timeout: parseNumber(import.meta.env.VITE_API_TIMEOUT, 30000),
    maxRetries: parseNumber(import.meta.env.VITE_API_MAX_RETRIES, 3),
  },

  // Application Configuration
  app: {
    name: import.meta.env.VITE_APP_NAME || 'CloudAudit',
    version: import.meta.env.VITE_APP_VERSION || '1.0.0',
    environment: validateEnvironment(import.meta.env.VITE_APP_ENV),
    baseUrl: import.meta.env.VITE_APP_BASE_URL || '/',
  },

  // Security Configuration
  security: {
    enableSecurityHeaders: parseBoolean(import.meta.env.VITE_ENABLE_SECURITY_HEADERS, false),
    cspMode: import.meta.env.VITE_CSP_MODE === 'report-only' ? 'report-only' : 'enforce',
    forceHttps: parseBoolean(import.meta.env.VITE_FORCE_HTTPS, false),
    sessionTimeout: parseNumber(import.meta.env.VITE_SESSION_TIMEOUT, 60),
  },

  // Feature Flags
  features: {
    enableReactQueryDevtools: parseBoolean(import.meta.env.VITE_ENABLE_REACT_QUERY_DEVTOOLS, true),
    enablePerformanceMonitoring: parseBoolean(import.meta.env.VITE_ENABLE_PERFORMANCE_MONITORING, false),
    enableErrorReporting: parseBoolean(import.meta.env.VITE_ENABLE_ERROR_REPORTING, false),
    enableAnalytics: parseBoolean(import.meta.env.VITE_ENABLE_ANALYTICS, false),
  },

  // Logging Configuration
  logging: {
    level: (['debug', 'info', 'warn', 'error'].includes(import.meta.env.VITE_LOG_LEVEL || '')) 
      ? import.meta.env.VITE_LOG_LEVEL as 'debug' | 'info' | 'warn' | 'error'
      : 'info',
    enableConsoleLogging: parseBoolean(import.meta.env.VITE_ENABLE_CONSOLE_LOGGING, true),
    enableRemoteLogging: parseBoolean(import.meta.env.VITE_ENABLE_REMOTE_LOGGING, false),
    remoteLogEndpoint: import.meta.env.VITE_REMOTE_LOG_ENDPOINT,
  },

  // Performance Configuration
  performance: {
    enableServiceWorker: parseBoolean(import.meta.env.VITE_ENABLE_SERVICE_WORKER, false),
    cacheStrategy: (['cache-first', 'network-first', 'stale-while-revalidate'].includes(import.meta.env.VITE_CACHE_STRATEGY || ''))
      ? import.meta.env.VITE_CACHE_STRATEGY as 'cache-first' | 'network-first' | 'stale-while-revalidate'
      : 'stale-while-revalidate',
    cacheDuration: parseNumber(import.meta.env.VITE_CACHE_DURATION, 3600),
  },

  // Third-party Services
  services: {
    sentryDsn: import.meta.env.VITE_SENTRY_DSN,
    gaTrackingId: import.meta.env.VITE_GA_TRACKING_ID,
    hotjarSiteId: import.meta.env.VITE_HOTJAR_SITE_ID,
  },

  // Build Configuration
  build: {
    target: import.meta.env.VITE_BUILD_TARGET || 'development',
    enableSourceMaps: parseBoolean(import.meta.env.VITE_ENABLE_SOURCE_MAPS, true),
    enableBundleAnalyzer: parseBoolean(import.meta.env.VITE_ENABLE_BUNDLE_ANALYZER, false),
  },

  // Development Configuration
  development: {
    port: parseNumber(import.meta.env.VITE_DEV_PORT, 5173),
    host: import.meta.env.VITE_DEV_HOST || '0.0.0.0',
    enableHmr: parseBoolean(import.meta.env.VITE_ENABLE_HMR, true),
    enableHttps: parseBoolean(import.meta.env.VITE_DEV_HTTPS, false),
  },

  // Deployment Configuration
  deployment: {
    platform: import.meta.env.VITE_DEPLOYMENT_PLATFORM,
    cdnBaseUrl: import.meta.env.VITE_CDN_BASE_URL,
    assetBaseUrl: import.meta.env.VITE_ASSET_BASE_URL,
  },

  // Monitoring Configuration
  monitoring: {
    healthCheckEndpoint: import.meta.env.VITE_HEALTH_CHECK_ENDPOINT || '/health',
    metricsInterval: parseNumber(import.meta.env.VITE_METRICS_INTERVAL, 60),
    enableRum: parseBoolean(import.meta.env.VITE_ENABLE_RUM, false),
  },
};

// Utility functions for common configuration checks
export const isProduction = () => config.app.environment === 'production';
export const isDevelopment = () => config.app.environment === 'development';
export const isStaging = () => config.app.environment === 'staging';

// Configuration validation
export const validateConfig = (): string[] => {
  const errors: string[] = [];

  // Validate required configuration
  if (!config.api.baseUrl) {
    errors.push('API base URL is required');
  }

  if (config.security.forceHttps && !config.api.baseUrl.startsWith('https://')) {
    errors.push('HTTPS is enforced but API URL is not HTTPS');
  }

  if (config.features.enableErrorReporting && !config.services.sentryDsn && !config.logging.remoteLogEndpoint) {
    errors.push('Error reporting is enabled but no error reporting service is configured');
  }

  return errors;
};

// Export configuration as default
export default config;
