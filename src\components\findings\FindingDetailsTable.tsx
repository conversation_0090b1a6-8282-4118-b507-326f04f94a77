import React from "react";
import { FindingDetailsTableProps } from "./types";
import FindingDetailsRow from "./FindingDetailsRow";

/**
 * FindingDetailsTable component renders a table of finding details
 * with proper handling of empty states and accessibility
 */
const FindingDetailsTable: React.FC<FindingDetailsTableProps> = ({
  finding,
  onRemediate,
  remediatingDetailIndex,
  isPending,
  onViewRemediationDetails,
}) => {
  // Check if there are any details to display
  const hasDetails = finding.details && finding.details.length > 0;

  // If no details, show empty state
  if (!hasDetails) {
    return (
      <div className="text-center py-8 text-dark-400" aria-live="polite">
        No detailed information available for this finding.
      </div>
    );
  }

  // Get the first detail to extract column headers
  const firstDetail = finding.details[0];

  // Get all keys except compliance and remediate for table headers
  const columnKeys = Object.keys(firstDetail).filter(
    (key) => key !== "compliance" && key !== "remediate"
  );

  // Check if compliance column should be displayed
  const showComplianceColumn = firstDetail.hasOwnProperty("compliance");

  return (
    <div
      className="overflow-x-auto"
      role="region"
      aria-label="Finding details"
      tabIndex={0}
    >
      <table className="w-full border-collapse">
        <thead>
          <tr className="bg-dark-800">
            {/* Render column headers */}
            {columnKeys.map((key) => (
              <th
                key={key}
                className="text-left py-3 px-4 text-dark-300 font-medium text-sm border-b border-dark-700"
                scope="col"
              >
                {finding.field_labels[key] || key}
              </th>
            ))}

            {/* Add compliance header if needed */}
            {showComplianceColumn && (
              <th
                key="compliance"
                className="text-left py-3 px-4 text-dark-300 font-medium text-sm border-b border-dark-700 min-w-[200px]"
                scope="col"
              >
                {finding.field_labels["compliance"] || "Compliance Status"}
              </th>
            )}
          </tr>
        </thead>
        <tbody>
          {/* Render detail rows */}
          {finding.details.map((detail, index) => (
            <FindingDetailsRow
              key={index}
              detail={detail}
              index={index}
              fieldLabels={finding.field_labels}
              findingId={finding.id}
              onRemediate={onRemediate}
              remediatingDetailIndex={remediatingDetailIndex}
              isPending={isPending}
              onViewRemediationDetails={onViewRemediationDetails}
            />
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default React.memo(FindingDetailsTable);
