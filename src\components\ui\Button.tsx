import React from "react";
import { cn } from "@utils/cn";

type ButtonVariant =
  | "primary"
  | "secondary"
  | "accent"
  | "success"
  | "error"
  | "outline"
  | "ghost";
type ButtonSize = "sm" | "md" | "lg";

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  isLoading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  fullWidth?: boolean;
  children: React.ReactNode;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      variant = "primary",
      size = "md",
      isLoading = false,
      leftIcon,
      rightIcon,
      fullWidth = false,
      className,
      children,
      disabled,
      ...props
    },
    ref
  ) => {
    const variantClasses = {
      primary: "bg-primary-500 hover:bg-primary-600 text-white shadow-md",
      secondary: "bg-secondary-500 hover:bg-secondary-600 text-white shadow-md",
      accent: "bg-accent-500 hover:bg-accent-600 text-white shadow-md",
      success: "bg-success-500 hover:bg-success-600 text-white shadow-md",
      error: "bg-error-500 hover:bg-error-600 text-white shadow-md",
      outline:
        "bg-transparent border border-dark-600 hover:bg-dark-800 text-dark-200",
      ghost: "bg-transparent hover:bg-dark-800 text-dark-200",
    };

    const sizeClasses = {
      sm: "py-1 px-3 text-sm",
      md: "py-2 px-4 text-base",
      lg: "py-3 px-6 text-lg",
    };

    return (
      <button
        ref={ref}
        disabled={disabled || isLoading}
        className={cn(
          "rounded-md font-medium transition-all duration-200 ease-in-out flex items-center justify-center gap-2",
          variantClasses[variant],
          sizeClasses[size],
          fullWidth ? "w-full" : "",
          disabled || isLoading
            ? "opacity-60 cursor-not-allowed"
            : "cursor-pointer",
          className
        )}
        {...props}
      >
        {isLoading && (
          <span className="w-4 h-4 border-2 border-dark-300 border-t-white rounded-full animate-spin mr-2" />
        )}
        {!isLoading && leftIcon}
        {children}
        {!isLoading && rightIcon}
      </button>
    );
  }
);

Button.displayName = "Button";

export default Button;
