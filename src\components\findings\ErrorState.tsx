import React from "react";
import Button from "@components/ui/Button";
import { <PERSON>ertCircle, ArrowLeft, RefreshCw } from "lucide-react";
import { ErrorStateProps } from "./types";
import { ServiceError } from "@/services/baseService";

/**
 * ErrorState component displays an error message with retry and back options
 * 
 * @param error - The error object
 * @param onRetry - Optional callback to retry the operation
 * @param onBack - Optional callback to navigate back
 */
const ErrorState: React.FC<ErrorStateProps> = ({ 
  error, 
  onRetry = () => window.location.reload(),
  onBack = () => window.history.back()
}) => {
  // Extract error message based on error type
  const errorMessage = React.useMemo(() => {
    if (error instanceof ServiceError) {
      return error.message;
    } else if (error instanceof Error) {
      return error.message;
    } else if (typeof error === 'string') {
      return error;
    }
    return "Failed to load finding details. Please try again.";
  }, [error]);

  return (
    <div 
      className="flex flex-col items-center justify-center h-full py-12"
      role="alert"
      aria-live="assertive"
    >
      <AlertCircle size={48} className="text-error-500 mb-4" aria-hidden="true" />
      
      <h2 className="text-xl font-semibold text-dark-100 mb-2">
        Error Loading Finding
      </h2>
      
      <p className="text-dark-400 mb-6 text-center max-w-md">
        {errorMessage}
      </p>
      
      <div className="flex gap-4">
        <Button 
          variant="outline" 
          onClick={onRetry}
          leftIcon={<RefreshCw size={16} />}
          aria-label="Retry loading the finding"
        >
          Retry
        </Button>
        
        <Button
          variant="primary"
          leftIcon={<ArrowLeft size={16} />}
          onClick={onBack}
          aria-label="Go back to previous page"
        >
          Go Back
        </Button>
      </div>
    </div>
  );
};

export default ErrorState;
