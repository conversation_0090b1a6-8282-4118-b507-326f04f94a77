import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { motion } from "framer-motion";
import {
  EyeIcon,
  EyeOffIcon,
  Lock,
  Mail,
  AlertCircle,
  CheckCircle2,
} from "lucide-react";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { Link, useLocation } from "react-router-dom";
import { z } from "zod";
import Button from "@components/ui/Button";
import Input from "@components/ui/Input";
import { useAuth } from "@/hooks/useAuth";

const schema = z.object({
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
});

type FormValues = z.infer<typeof schema>;

const Login: React.FC = () => {
  const location = useLocation();
  const { login } = useAuth();
  const [showPassword, setShowPassword] = useState(false);

  // Get credentials and message from navigation state
  const defaultValues = {
    email: location.state?.email || "",
    password: "",
  };

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormValues>({
    resolver: zodResolver(schema),
    defaultValues,
  });

  const onSubmit = async (data: FormValues) => {
    try {
      await login.mutateAsync(data);
    } catch (error) {
      console.error("Login error:", error);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-dark-100 mb-2">Welcome back!</h2>
        <p className="text-dark-400">Sign in to continue to CloudAudit</p>
      </div>

      {location.state?.message && (
        <div
          className={`mb-6 p-3 rounded-md text-sm flex items-start ${
            location.state.error
              ? "bg-error-500/10 border border-error-500/30 text-error-400"
              : "bg-success-500/10 border border-success-500/30 text-success-400"
          }`}
        >
          {location.state.error ? (
            <AlertCircle size={16} className="mr-2 mt-0.5 flex-shrink-0" />
          ) : (
            <CheckCircle2 size={16} className="mr-2 mt-0.5 flex-shrink-0" />
          )}
          <span>{location.state.message}</span>
        </div>
      )}

      {login.error && (
        <div className="mb-6 p-3 bg-error-500/10 border border-error-500/30 rounded-md text-error-400 text-sm flex items-start">
          <AlertCircle size={16} className="mr-2 mt-0.5 flex-shrink-0" />
          <span>{login.error.message}</span>
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <Input
          label="Email"
          type="email"
          placeholder="<EMAIL>"
          leftIcon={<Mail size={16} />}
          error={errors.email?.message}
          fullWidth
          {...register("email")}
        />

        <Input
          label="Password"
          type={showPassword ? "text" : "password"}
          placeholder="••••••••"
          leftIcon={<Lock size={16} />}
          rightIcon={
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="focus:outline-none"
            >
              {showPassword ? <EyeOffIcon size={16} /> : <EyeIcon size={16} />}
            </button>
          }
          error={errors.password?.message}
          fullWidth
          {...register("password")}
        />

        <div>
          <Button
            type="submit"
            variant="primary"
            isLoading={login.isPending}
            fullWidth
          >
            Sign In
          </Button>
        </div>
      </form>

      <div className="mt-8 text-center">
        <p className="text-dark-400">
          Don't have an account?{" "}
          <Link
            to="/signup"
            className="text-primary-500 hover:text-primary-400 transition-colors"
          >
            Sign up
          </Link>
        </p>
      </div>
    </motion.div>
  );
};

export default Login;
