import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { <PERSON><PERSON><PERSON><PERSON>outer } from "react-router-dom";
import {
  QueryClient,
  QueryClientProvider,
  QueryCache,
  MutationCache,
} from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import App from "./App";
import ErrorBoundary from "@/components/ErrorBoundary";
import { config, validateConfig } from "./lib/config";
import logger from "./lib/logger";
import "./index.css";

// Validate configuration on startup
const configErrors = validateConfig();
if (configErrors.length > 0) {
  console.error("Configuration validation failed:", configErrors);
  if (config.app.environment === "production") {
    // In production, log errors but don't prevent startup
    logger.error("Configuration validation failed", { errors: configErrors });
  }
}

// Log application startup
logger.info("Application starting", {
  version: config.app.version,
  environment: config.app.environment,
  timestamp: new Date().toISOString(),
});

// Create React Query caches with error handlers
const queryCache = new QueryCache({
  onError: (error: any, query: any) => {
    logger.error("React Query error", error, {
      queryKey: query.queryKey,
      queryHash: query.queryHash,
    });
  },
});

const mutationCache = new MutationCache({
  onError: (error: any, variables: any, _context: any, mutation: any) => {
    logger.error("React Query mutation error", error, {
      mutationKey: mutation.options.mutationKey,
      variables,
    });
  },
});

// Create React Query client with environment-specific configuration
const queryClient = new QueryClient({
  queryCache,
  mutationCache,
  defaultOptions: {
    queries: {
      // Retry configuration based on environment
      retry: config.app.environment === "production" ? 3 : 1,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),

      // Cache configuration
      staleTime: config.performance.cacheDuration * 1000,
      gcTime: config.performance.cacheDuration * 2 * 1000,

      // Error handling
      throwOnError: false,

      // Refetch configuration
      refetchOnWindowFocus: config.app.environment === "production",
      refetchOnReconnect: true,
    },
    mutations: {
      retry: false,
      throwOnError: false,
    },
  },
});

// Error boundary error handler
const handleErrorBoundaryError = (error: Error, errorInfo: React.ErrorInfo) => {
  logger.error("React Error Boundary caught error", error, {
    componentStack: errorInfo.componentStack,
  });
};

// Performance monitoring
if (config.features.enablePerformanceMonitoring) {
  // Monitor Core Web Vitals
  import("web-vitals")
    .then((webVitals: any) => {
      if (webVitals.getCLS) {
        webVitals.getCLS((metric: any) =>
          logger.performance("CLS", metric.value, metric)
        );
      }
      if (webVitals.getFID) {
        webVitals.getFID((metric: any) =>
          logger.performance("FID", metric.value, metric)
        );
      }
      if (webVitals.getFCP) {
        webVitals.getFCP((metric: any) =>
          logger.performance("FCP", metric.value, metric)
        );
      }
      if (webVitals.getLCP) {
        webVitals.getLCP((metric: any) =>
          logger.performance("LCP", metric.value, metric)
        );
      }
      if (webVitals.getTTFB) {
        webVitals.getTTFB((metric: any) =>
          logger.performance("TTFB", metric.value, metric)
        );
      }
    })
    .catch((error) => {
      logger.warn("Failed to load web-vitals", error);
    });
}

// Initialize error reporting
if (config.features.enableErrorReporting && config.services.sentryDsn) {
  import("@sentry/react")
    .then((Sentry) => {
      Sentry.init({
        dsn: config.services.sentryDsn,
        environment: config.app.environment,
        release: config.app.version,
        tracesSampleRate: config.app.environment === "production" ? 0.1 : 1.0,
        beforeSend(event) {
          // Filter out development errors in production
          if (config.app.environment === "production" && event.exception) {
            const error = event.exception.values?.[0];
            if (error?.value?.includes("Non-Error promise rejection")) {
              return null;
            }
          }
          return event;
        },
      });
    })
    .catch(() => {
      logger.warn("Failed to initialize Sentry");
    });
}

// Remove loading spinner
const loadingSpinner = document.querySelector(".loading-spinner");
if (loadingSpinner) {
  loadingSpinner.remove();
}

// Render application
createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <ErrorBoundary onError={handleErrorBoundaryError}>
      <BrowserRouter>
        <QueryClientProvider client={queryClient}>
          <App />
          {config.features.enableReactQueryDevtools && (
            <ReactQueryDevtools initialIsOpen={false} />
          )}
        </QueryClientProvider>
      </BrowserRouter>
    </ErrorBoundary>
  </StrictMode>
);
