import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@components/ui/Card";
import { useRemediateFinding } from "@/hooks/useScans";
import { FindingDetailsCardProps } from "./types";
import FindingDetailsTable from "./FindingDetailsTable";
import RemediationDetailsModal from "./RemediationDetailsModal";
import { FindingDetailItem } from "@/types/api";
import { usePermissionContext } from "@/contexts/PermissionContext";
import { PERMISSIONS } from "@/types/permissions";
import toast from "react-hot-toast";

/**
 * FindingDetailsCard component displays a card with detailed information about a finding
 * including a table of details and remediation functionality
 */
const FindingDetailsCard: React.FC<FindingDetailsCardProps> = ({ finding }) => {
  // Get remediation mutation
  const remediateMutation = useRemediateFinding();

  // Get permission context
  const { hasPermission } = usePermissionContext();

  // Check if user has permission to remediate findings
  const canRemediate = hasPermission(PERMISSIONS.REMEDIATE_FINDINGS);

  // State for tracking which detail is being remediated
  const [remediatingDetailIndex, setRemediatingDetailIndex] = useState<
    number | null
  >(null);

  // State for the remediation details modal
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedDetail, setSelectedDetail] = useState<{
    detail: FindingDetailItem;
    index: number;
  } | null>(null);

  // Function to open the remediation details modal
  const openRemediationModal = React.useCallback(
    (detail: FindingDetailItem, index: number) => {
      setSelectedDetail({ detail, index });
      setIsModalOpen(true);
    },
    []
  );

  // Function to close the remediation details modal
  const closeRemediationModal = React.useCallback(() => {
    setIsModalOpen(false);
    // Wait for the modal animation to complete before clearing the selected detail
    setTimeout(() => {
      setSelectedDetail(null);
    }, 300);
  }, []);

  // Function to handle remediation
  const handleRemediate = React.useCallback(
    async (
      findingId: number,
      detailIndex: number,
      detailData: Record<string, any>
    ) => {
      // Check if user has permission to remediate findings
      if (!canRemediate) {
        toast.error("You don't have permission to remediate findings");
        return;
      }

      setRemediatingDetailIndex(detailIndex);
      try {
        // Create a copy of the detail data to send to the API
        const remediationData = { ...detailData };

        await remediateMutation.mutateAsync({
          findingId,
          detailData: remediationData,
        });

        // Close the modal if it's open after successful remediation
        if (isModalOpen) {
          closeRemediationModal();
        }
      } catch (error) {
        // Error is handled in the mutation hook
      } finally {
        setRemediatingDetailIndex(null);
      }
    },
    [remediateMutation, isModalOpen, closeRemediationModal, canRemediate]
  );

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Finding Details</CardTitle>
          <CardDescription>
            Detailed information about this finding
          </CardDescription>
        </CardHeader>
        <CardContent>
          <FindingDetailsTable
            finding={finding}
            onRemediate={handleRemediate}
            remediatingDetailIndex={remediatingDetailIndex}
            isPending={remediateMutation.isPending}
            onViewRemediationDetails={openRemediationModal}
          />
        </CardContent>
      </Card>

      {/* Remediation Details Modal */}
      {selectedDetail && (
        <RemediationDetailsModal
          isOpen={isModalOpen}
          onClose={closeRemediationModal}
          detail={selectedDetail.detail}
          detailIndex={selectedDetail.index}
          findingId={finding.id}
          onRemediate={handleRemediate}
          isRemediating={
            remediateMutation.isPending &&
            remediatingDetailIndex === selectedDetail.index
          }
        />
      )}
    </>
  );
};

export default React.memo(FindingDetailsCard);
