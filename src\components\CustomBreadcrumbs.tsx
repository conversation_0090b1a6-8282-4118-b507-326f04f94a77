import { ChevronRight } from "lucide-react";
import React from "react";
import { Link, useParams, useSearchParams } from "react-router-dom";
import { useFindingDetail } from "@/hooks/useScans";

/**
 * Breadcrumb type to determine the structure and behavior of the breadcrumbs
 */
export type BreadcrumbType = "findings" | "findingDetail";

/**
 * Props for the CustomBreadcrumbs component
 */
interface CustomBreadcrumbsProps {
  /**
   * The type of breadcrumb to display
   * - 'findings': For the ScanFindings page (4 levels)
   * - 'findingDetail': For the FindingDetail page (5 levels)
   */
  type: BreadcrumbType;
}

/**
 * CustomBreadcrumbs component that handles breadcrumb navigation for scan findings pages
 * This component ensures that the service parameter is preserved when navigating
 * and adapts its structure based on the page type (findings list or finding detail)
 */
const CustomBreadcrumbs: React.FC<CustomBreadcrumbsProps> = ({ type }) => {
  // Get URL parameters
  const { scanId, findingId } = useParams<{ scanId: string; findingId: string }>();
  const [searchParams] = useSearchParams();
  const serviceName = searchParams.get("service");
  
  // Only fetch finding details if we're on the finding detail page and need it
  const { data: findingDetailData } = useFindingDetail(
    type === "findingDetail" && findingId && !serviceName ? 
      parseInt(findingId, 10) : null,
    { enabled: type === "findingDetail" && !!findingId && !serviceName }
  );
  
  // Get service name from either URL or finding details
  const serviceNameToUse = serviceName || 
    (type === "findingDetail" ? findingDetailData?.data?.service_name : null);

  // If we don't have a service name yet, don't render breadcrumbs
  if (!serviceNameToUse) {
    return null;
  }

  // Create base breadcrumb items
  const breadcrumbItems = [
    { 
      label: "Dashboard", 
      path: "/dashboard" 
    },
    { 
      label: "Scans", 
      path: "/dashboard/scans" 
    },
    { 
      label: `Scan #${scanId}`, 
      path: `/dashboard/scans/${scanId}` 
    },
    { 
      label: "Findings", 
      path: `/dashboard/scans/${scanId}/findings?service=${serviceNameToUse}` 
    },
  ];
  
  // Add finding detail item if needed
  if (type === "findingDetail" && findingId) {
    breadcrumbItems.push({
      label: `Finding #${findingId}`,
      path: `/dashboard/scans/${scanId}/findings/${findingId}?service=${serviceNameToUse}`,
    });
  }

  return (
    <nav className="flex items-center text-sm mb-4" aria-label="Breadcrumb">
      <ol className="flex items-center flex-wrap">
        {breadcrumbItems.map((item, index) => {
          const isLast = index === breadcrumbItems.length - 1;

          return (
            <li key={`${item.path}-${index}`} className="flex items-center">
              {index > 0 && (
                <ChevronRight size={14} className="mx-2 text-dark-400" />
              )}

              {isLast ? (
                <span className="text-dark-200 font-medium">{item.label}</span>
              ) : (
                <Link
                  to={item.path}
                  className="text-dark-400 hover:text-primary-400 transition-colors"
                >
                  {item.label}
                </Link>
              )}
            </li>
          );
        })}
      </ol>
    </nav>
  );
};

export default CustomBreadcrumbs;
