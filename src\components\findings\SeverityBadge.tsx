import React from "react";
import { SeverityBadgeProps } from "./types";
import { cn } from "@/utils/cn";

/**
 * SeverityBadge component displays a badge with the severity level of a finding
 * 
 * @param severity - The severity level to display (critical, high, medium, low)
 * @param size - The size of the badge (sm, md, lg)
 */
const SeverityBadge: React.FC<SeverityBadgeProps> = ({ 
  severity,
  size = "md" 
}) => {
  // Normalize severity to lowercase for consistent comparison
  const normalizedSeverity = severity.toLowerCase();
  
  // Determine the appropriate classes based on severity and size
  const containerClasses = cn(
    "inline-flex items-center justify-center rounded-full font-medium uppercase",
    {
      "bg-error-500/20 text-error-500": normalizedSeverity === "critical",
      "bg-error-400/20 text-error-400": normalizedSeverity === "high",
      "bg-warning-500/20 text-warning-500": normalizedSeverity === "medium",
      "bg-info-500/20 text-info-500": normalizedSeverity === "low",
      "px-1.5 py-0.5 text-xs": size === "sm",
      "px-2 py-1 text-xs": size === "md",
      "px-3 py-1.5 text-sm": size === "lg",
    }
  );

  return (
    <div 
      className={containerClasses} 
      role="status" 
      aria-label={`Severity: ${severity}`}
    >
      {severity}
    </div>
  );
};

export default SeverityBadge;
