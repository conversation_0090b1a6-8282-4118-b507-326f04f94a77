{"ci": {"collect": {"numberOfRuns": 3, "settings": {"chromeFlags": "--no-sandbox --disable-dev-shm-usage", "preset": "desktop", "throttling": {"rttMs": 40, "throughputKbps": 10240, "cpuSlowdownMultiplier": 1, "requestLatencyMs": 0, "downloadThroughputKbps": 0, "uploadThroughputKbps": 0}}}, "assert": {"assertions": {"categories:performance": ["error", {"minScore": 0.9}], "categories:accessibility": ["error", {"minScore": 0.9}], "categories:best-practices": ["error", {"minScore": 0.9}], "categories:seo": ["error", {"minScore": 0.9}], "categories:pwa": ["warn", {"minScore": 0.8}]}}, "upload": {"target": "temporary-public-storage"}}}