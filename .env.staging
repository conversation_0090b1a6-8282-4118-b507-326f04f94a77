# CloudAudit Frontend - Staging Environment
# This file contains staging-specific environment variables

# =============================================================================
# API Configuration
# =============================================================================
VITE_API_BASE_URL=https://staging-api.cloudaudit.com
VITE_API_TIMEOUT=30000
VITE_API_MAX_RETRIES=3

# =============================================================================
# Application Configuration
# =============================================================================
VITE_APP_ENV=staging
VITE_APP_NAME=CloudAudit (Staging)
VITE_APP_VERSION=1.0.0-staging
VITE_APP_BASE_URL=/

# =============================================================================
# Security Configuration
# =============================================================================
VITE_ENABLE_SECURITY_HEADERS=true
VITE_CSP_MODE=enforce
VITE_FORCE_HTTPS=true
VITE_SESSION_TIMEOUT=60

# =============================================================================
# Feature Flags
# =============================================================================
VITE_ENABLE_REACT_QUERY_DEVTOOLS=true
VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_ENABLE_ERROR_REPORTING=true
VITE_ENABLE_ANALYTICS=false

# =============================================================================
# Logging Configuration
# =============================================================================
VITE_LOG_LEVEL=warn
VITE_ENABLE_CONSOLE_LOGGING=true
VITE_ENABLE_REMOTE_LOGGING=true
VITE_REMOTE_LOG_ENDPOINT=https://staging-logs.cloudaudit.com/api/logs

# =============================================================================
# Performance Configuration
# =============================================================================
VITE_ENABLE_SERVICE_WORKER=true
VITE_CACHE_STRATEGY=network-first
VITE_CACHE_DURATION=1800

# =============================================================================
# Third-Party Services
# =============================================================================
VITE_SENTRY_DSN=https://<EMAIL>/project-id
VITE_GA_TRACKING_ID=
VITE_HOTJAR_SITE_ID=

# =============================================================================
# Build Configuration
# =============================================================================
VITE_BUILD_TARGET=staging
VITE_ENABLE_SOURCE_MAPS=true
VITE_ENABLE_BUNDLE_ANALYZER=false

# =============================================================================
# Deployment Configuration
# =============================================================================
VITE_DEPLOYMENT_PLATFORM=vercel
VITE_CDN_BASE_URL=https://staging-cdn.cloudaudit.com
VITE_ASSET_BASE_URL=https://staging-assets.cloudaudit.com

# =============================================================================
# Monitoring & Observability
# =============================================================================
VITE_HEALTH_CHECK_ENDPOINT=/health
VITE_METRICS_INTERVAL=60
VITE_ENABLE_RUM=true
