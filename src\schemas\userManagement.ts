import { z } from 'zod';

// Define the role schema
const roleSchema = z.object({
  id: z.number(),
  name: z.string(),
  is_custom: z.boolean().optional(),
});

// Schema for creating a new user
export const createUserSchema = z.object({
  first_name: z
    .string()
    .min(1, { message: 'First name is required' })
    .max(50, { message: 'First name must be less than 50 characters' }),
  last_name: z
    .string()
    .min(1, { message: 'Last name is required' })
    .max(50, { message: 'Last name must be less than 50 characters' }),
  email: z
    .string()
    .min(1, { message: 'Email is required' })
    .email({ message: 'Please enter a valid email address' }),
  password: z
    .string()
    .min(8, { message: 'Password must be at least 8 characters' }),
  role: roleSchema.nullable().refine((val) => val !== null, {
    message: 'Please select a role'
  }),
});

// Type inference from the schema
export type CreateUserFormValues = z.infer<typeof createUserSchema>;

// Schema for editing a user
export const editUserSchema = z.object({
  first_name: z
    .string()
    .min(1, { message: 'First name is required' })
    .max(50, { message: 'First name must be less than 50 characters' }),
  last_name: z
    .string()
    .min(1, { message: 'Last name is required' })
    .max(50, { message: 'Last name must be less than 50 characters' }),
  email: z
    .string()
    .min(1, { message: 'Email is required' })
    .email({ message: 'Please enter a valid email address' }),
  role: roleSchema.nullable().refine((val) => val !== null, {
    message: 'Please select a role'
  }),
});

// Type inference from the schema
export type EditUserFormValues = z.infer<typeof editUserSchema>;
