# CloudAudit Frontend - Production Environment
# This file contains production-specific environment variables

# =============================================================================
# API Configuration
# =============================================================================
VITE_API_BASE_URL=http://192.168.2.112:8000
VITE_API_TIMEOUT=30000
VITE_API_MAX_RETRIES=3

# =============================================================================
# Application Configuration
# =============================================================================
VITE_APP_ENV=production
VITE_APP_NAME=CloudAudit
VITE_APP_VERSION=1.0.0
VITE_APP_BASE_URL=/

# =============================================================================
# Security Configuration
# =============================================================================
VITE_ENABLE_SECURITY_HEADERS=true
VITE_CSP_MODE=enforce
VITE_FORCE_HTTPS=true
VITE_SESSION_TIMEOUT=60

# =============================================================================
# Feature Flags
# =============================================================================
VITE_ENABLE_REACT_QUERY_DEVTOOLS=false
VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_ENABLE_ERROR_REPORTING=true
VITE_ENABLE_ANALYTICS=true

# =============================================================================
# Logging Configuration
# =============================================================================
VITE_LOG_LEVEL=error
VITE_ENABLE_CONSOLE_LOGGING=false
VITE_ENABLE_REMOTE_LOGGING=true
VITE_REMOTE_LOG_ENDPOINT=https://logs.cloudaudit.com/api/logs

# =============================================================================
# Performance Configuration
# =============================================================================
VITE_ENABLE_SERVICE_WORKER=true
VITE_CACHE_STRATEGY=stale-while-revalidate
VITE_CACHE_DURATION=3600

# =============================================================================
# Third-Party Services
# =============================================================================
VITE_SENTRY_DSN=https://<EMAIL>/project-id
VITE_GA_TRACKING_ID=GA-XXXXXXXXX-X
VITE_HOTJAR_SITE_ID=

# =============================================================================
# Build Configuration
# =============================================================================
VITE_BUILD_TARGET=production
VITE_ENABLE_SOURCE_MAPS=false
VITE_ENABLE_BUNDLE_ANALYZER=false

# =============================================================================
# Deployment Configuration
# =============================================================================
VITE_DEPLOYMENT_PLATFORM=vercel
VITE_CDN_BASE_URL=https://cdn.cloudaudit.com
VITE_ASSET_BASE_URL=https://assets.cloudaudit.com

# =============================================================================
# Monitoring & Observability
# =============================================================================
VITE_HEALTH_CHECK_ENDPOINT=/health
VITE_METRICS_INTERVAL=60
VITE_ENABLE_RUM=true
