# Security Headers for CloudAudit Frontend
# This file is used by Netlify, Vercel, and other platforms for setting HTTP headers

/*
  # Content Security Policy
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://fonts.googleapis.com https://www.googletagmanager.com https://www.google-analytics.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.cloudaudit.com https://staging-api.cloudaudit.com http://*************:8000 https://www.google-analytics.com; frame-ancestors 'none'; base-uri 'self'; form-action 'self';

  # Prevent MIME type sniffing
  X-Content-Type-Options: nosniff

  # Enable XSS protection
  X-XSS-Protection: 1; mode=block

  # Prevent clickjacking
  X-Frame-Options: DENY

  # Strict Transport Security (HTTPS only)
  Strict-Transport-Security: max-age=31536000; includeSubDomains; preload

  # Referrer Policy
  Referrer-Policy: strict-origin-when-cross-origin

  # Permissions Policy
  Permissions-Policy: camera=(), microphone=(), geolocation=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()

  # Remove server information
  Server: CloudAudit

  # Cache control for HTML files
  Cache-Control: no-cache, no-store, must-revalidate

/assets/*
  # Cache static assets for 1 year
  Cache-Control: public, max-age=31536000, immutable

/assets/js/*
  # Cache JavaScript files for 1 year
  Cache-Control: public, max-age=31536000, immutable

/assets/css/*
  # Cache CSS files for 1 year
  Cache-Control: public, max-age=31536000, immutable

/assets/images/*
  # Cache images for 1 year
  Cache-Control: public, max-age=31536000, immutable

/assets/fonts/*
  # Cache fonts for 1 year
  Cache-Control: public, max-age=31536000, immutable

/logo.svg
  # Cache logo for 1 week
  Cache-Control: public, max-age=604800

/manifest.json
  # Cache manifest for 1 day
  Cache-Control: public, max-age=86400

/robots.txt
  # Cache robots.txt for 1 day
  Cache-Control: public, max-age=86400

/sitemap.xml
  # Cache sitemap for 1 day
  Cache-Control: public, max-age=86400
