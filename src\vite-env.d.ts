/// <reference types="vite/client" />

// Extend the Window interface to include Sentry types
declare global {
    interface Window {
        Sentry?: {
            captureException: (error: Error, options?: {
                contexts?: Record<string, any>;
                tags?: Record<string, any>;
                extra?: Record<string, any>;
            }) => void;
            captureMessage: (message: string, level?: 'debug' | 'info' | 'warning' | 'error' | 'fatal') => void;
            setUser: (user: { id?: string; email?: string; username?: string;[key: string]: any }) => void;
            setTag: (key: string, value: string) => void;
            setContext: (key: string, context: Record<string, any>) => void;
            addBreadcrumb: (breadcrumb: {
                message?: string;
                category?: string;
                level?: 'debug' | 'info' | 'warning' | 'error' | 'fatal';
                data?: Record<string, any>;
            }) => void;
        };
    }
}
