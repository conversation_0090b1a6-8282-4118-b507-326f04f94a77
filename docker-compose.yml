# CloudAudit Frontend - Docker Compose Configuration

version: '3.8'

services:
  # Development service
  frontend-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: cloudaudit-frontend-dev
    ports:
      - "5173:5173"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - VITE_APP_ENV=development
      - VITE_API_BASE_URL=http://localhost:8000
      - VITE_ENABLE_REACT_QUERY_DEVTOOLS=true
      - VITE_LOG_LEVEL=debug
    networks:
      - cloudaudit-network
    profiles:
      - development

  # Production service
  frontend-prod:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: cloudaudit-frontend-prod
    ports:
      - "80:80"
      - "443:443"
    environment:
      - NODE_ENV=production
      - VITE_APP_ENV=production
    volumes:
      - ./ssl:/etc/nginx/ssl:ro
    networks:
      - cloudaudit-network
    profiles:
      - production
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Staging service
  frontend-staging:
    build:
      context: .
      dockerfile: Dockerfile
      target: staging
    container_name: cloudaudit-frontend-staging
    ports:
      - "8080:80"
    environment:
      - NODE_ENV=production
      - VITE_APP_ENV=staging
      - VITE_API_BASE_URL=https://staging-api.cloudaudit.com
    networks:
      - cloudaudit-network
    profiles:
      - staging
    restart: unless-stopped

  # Nginx reverse proxy (for production)
  nginx-proxy:
    image: nginx:alpine
    container_name: cloudaudit-nginx-proxy
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx-proxy.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx-cache:/var/cache/nginx
    depends_on:
      - frontend-prod
    networks:
      - cloudaudit-network
    profiles:
      - production-proxy
    restart: unless-stopped

  # Redis for caching (optional)
  redis:
    image: redis:alpine
    container_name: cloudaudit-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - cloudaudit-network
    profiles:
      - production
      - staging
    restart: unless-stopped
    command: redis-server --appendonly yes

networks:
  cloudaudit-network:
    driver: bridge
    name: cloudaudit-network

volumes:
  nginx-cache:
    name: cloudaudit-nginx-cache
  redis-data:
    name: cloudaudit-redis-data
