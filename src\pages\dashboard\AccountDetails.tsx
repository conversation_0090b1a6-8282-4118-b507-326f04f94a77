import ScanModal from "@components/ScanModal";
import { formatDate } from "@/lib/dateUtils";
import Button from "@components/ui/Button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@components/ui/Card";
import Modal from "@components/ui/Modal";
import {
  useAccountDetail,
  useDeleteAccount,
  QUERY_KEYS as ACCOUNT_QUERY_KEYS,
} from "@hooks/useAccounts";
import {
  AlertCircle,
  CheckCircle,
  Cloud,
  CloudIcon,
  Database,
  Eye,
  EyeOff,
  Key,
  Loader2,
  RefreshCw,
  Shield,
  Trash2,
  XCircle,
} from "lucide-react";
import React, { useState, useCallback } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

const AccountDetails: React.FC = () => {
  const { accountId: accountIdParam } = useParams<{ accountId: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  // Use the new React Query hooks
  const accountId = accountIdParam ? parseInt(accountIdParam, 10) : null;
  const {
    data: accountDetailResponse,
    isLoading,
    error,
    refetch: refetchAccountDetails,
  } = useAccountDetail(accountId);
  const { mutateAsync: deleteAccountMutation } = useDeleteAccount();

  // Extract account detail from the response
  const accountDetail = accountDetailResponse?.data;

  const [scanModalOpen, setScanModalOpen] = useState(false);
  const [showSecret, setShowSecret] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [isLaunchingScan, setIsLaunchingScan] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Handle manual refresh of account details
  const handleRefresh = useCallback(async () => {
    if (isRefreshing) return;

    try {
      setIsRefreshing(true);
      await refetchAccountDetails();
      toast.success("Account details refreshed successfully");
    } catch (error) {
      toast.error("Failed to refresh account details");
    } finally {
      setIsRefreshing(false);
    }
  }, [refetchAccountDetails, isRefreshing]);

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Loader2 className="animate-spin mr-2" size={24} />
        <p className="text-dark-400">Loading account details...</p>
      </div>
    );
  }

  // Show error state
  if (error || !accountDetail) {
    return (
      <div className="flex items-center justify-center h-full">
        <AlertCircle className="text-error-500 mr-2" size={24} />
        <p className="text-dark-400">
          {error instanceof Error
            ? error.message
            : "Failed to load account details"}
        </p>
      </div>
    );
  }

  const handleDeleteAccount = () => {
    setShowDeleteConfirm(true);
    setDeleteError(null);
  };

  const handleDeleteConfirm = async () => {
    if (!accountDetail) return;

    try {
      // Use the mutation from useDeleteAccount hook
      await deleteAccountMutation(accountDetail.id);
      navigate("/dashboard");
    } catch (error) {
      setDeleteError(
        error instanceof Error ? error.message : "Failed to delete account"
      );
    }
  };

  const getProviderIcon = (providerName: string) => {
    switch (providerName) {
      case "AWS":
        return <CloudIcon size={32} className="text-accent-500" />;
      case "GCP":
        return <Database size={32} className="text-primary-500" />;
      case "Azure":
        return <Cloud size={32} className="text-secondary-500" />;
      default:
        return <Cloud size={32} />;
    }
  };

  // Use the cloud_provider_name directly from the API response
  const providerName = accountDetail.cloud_provider_name;
  const cloudProviderId = accountDetail.cloud_provider_id;

  return (
    <div className="space-y-8">
      {deleteError && (
        <div className="p-3 bg-error-500/10 border border-error-500/30 rounded-md text-error-400 text-sm flex items-start">
          <AlertCircle size={16} className="mr-2 mt-0.5 flex-shrink-0" />
          <span>{deleteError}</span>
        </div>
      )}

      {/* Account Header with Refresh Button */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold text-dark-100">
          Account Details
        </h1>
        <Button
          variant="outline"
          size="sm"
          onClick={handleRefresh}
          disabled={isRefreshing || isLoading}
          leftIcon={
            <RefreshCw
              size={16}
              className={isRefreshing ? "animate-spin" : ""}
            />
          }
        >
          {isRefreshing ? "Refreshing..." : "Refresh"}
        </Button>
      </div>

      {/* Account Header */}
      <Card>
        <CardContent>
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6 p-6">
            <div className="flex items-start gap-4">
              <div className="p-3 rounded-lg bg-dark-800 border border-dark-700">
                {getProviderIcon(providerName)}
              </div>

              <div>
                <h2 className="text-xl font-bold text-dark-100">
                  {providerName}
                </h2>
                <span className="font-mono text-base font-normal text-dark-400">
                  {accountDetail.account_name}
                </span>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-3">
              <Button
                variant="primary"
                leftIcon={<Shield size={16} />}
                onClick={() => {
                  setIsLaunchingScan(true);
                  setScanModalOpen(true);
                }}
                isLoading={isLaunchingScan}
                disabled={isLaunchingScan}
              >
                Launch Scan
              </Button>
              <Button
                variant="error"
                leftIcon={<Trash2 size={16} />}
                onClick={handleDeleteAccount}
              >
                Delete Account
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Account Details */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left column */}
        <div className="lg:col-span-2 space-y-8">
          {/* Account Information */}
          <Card>
            <CardHeader>
              <CardTitle>Account Information</CardTitle>
              <CardDescription>
                General information about this cloud account
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-1">
                  <p className="text-xs text-dark-400">Account Name</p>
                  <p className="text-sm font-medium text-dark-100">
                    {accountDetail.account_name}
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="text-xs text-dark-400">Created At</p>
                  <p className="text-sm text-dark-100">
                    {formatDate(accountDetail?.created_at, {
                      includeTime: true,
                    })}
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="text-xs text-dark-400">Last Scan Date</p>
                  <p className="text-sm text-dark-100">
                    {accountDetail.recent_scans?.scan_start
                      ? formatDate(accountDetail?.recent_scans.scan_start, {
                          includeTime: true,
                        })
                      : "No scans yet"}
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="text-xs text-dark-400">Security Status</p>
                  <p
                    className={`text-sm font-medium ${
                      accountDetail.recent_scans?.failed_findings
                        ? "text-error-400"
                        : "text-success-400"
                    }`}
                  >
                    {accountDetail.recent_scans?.failed_findings
                      ? `${accountDetail.recent_scans.failed_findings} issues found`
                      : "No issues detected"}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Recent Scans */}
          <Card>
            <CardContent>
              {isLaunchingScan ? (
                <div className="py-12 text-center">
                  <Loader2 className="animate-spin mx-auto mb-4" size={32} />
                  <p className="text-dark-400">
                    Launching scan for this account...
                  </p>
                </div>
              ) : accountDetail.recent_scans &&
                Object.keys(accountDetail.recent_scans).length > 0 ? (
                <div className="border border-dark-700 rounded-md overflow-hidden">
                  <div className="bg-dark-800 p-4 flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-medium text-dark-100">
                        Scan
                      </h3>
                      <div className="flex flex-col gap-1">
                        <p className="text-sm text-dark-400">
                          <span className="text-dark-500">Started:</span>{" "}
                          {formatDate(
                            accountDetail?.recent_scans?.scan_start ||
                              new Date(),
                            { includeTime: true }
                          )}
                        </p>
                        {accountDetail.recent_scans?.scan_end && (
                          <p className="text-sm text-dark-400">
                            <span className="text-dark-500">Completed:</span>{" "}
                            {formatDate(accountDetail?.recent_scans?.scan_end, {
                              includeTime: true,
                            })}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center">
                      <div
                        className={`px-3 py-1 rounded-full text-xs font-medium ${
                          accountDetail.recent_scans?.status === "completed"
                            ? "bg-success-500/10 text-success-400"
                            : accountDetail.recent_scans?.status === "failed"
                            ? "bg-error-500/10 text-error-400"
                            : "bg-warning-500/10 text-warning-400"
                        }`}
                      >
                        {accountDetail.recent_scans?.status === "completed" ? (
                          <CheckCircle
                            size={14}
                            className="inline-block mr-1"
                          />
                        ) : accountDetail.recent_scans?.status === "failed" ? (
                          <XCircle size={14} className="inline-block mr-1" />
                        ) : (
                          <Loader2
                            size={14}
                            className="inline-block mr-1 animate-spin"
                          />
                        )}
                        {(accountDetail.recent_scans?.status || "")
                          .charAt(0)
                          .toUpperCase() +
                          (accountDetail.recent_scans?.status || "").slice(1)}
                      </div>
                    </div>
                  </div>
                  <div className="p-4 bg-dark-900">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-1">
                        <p className="text-xs text-dark-400">Scan ID</p>
                        <p className="text-lg font-mono text-dark-300">
                          #{accountDetail.recent_scans?.id || 0}
                        </p>
                      </div>
                      <div className="space-y-1">
                        <p className="text-xs text-dark-400">Passed Checks</p>
                        <p className="text-lg font-medium text-success-400">
                          {(accountDetail.recent_scans?.findings_count || 0) -
                            (accountDetail.recent_scans?.failed_findings || 0)}
                        </p>
                      </div>
                      <div className="space-y-1">
                        <p className="text-xs text-dark-400">Total Findings</p>
                        <p className="text-lg font-medium text-dark-100">
                          {accountDetail.recent_scans?.findings_count || 0}
                        </p>
                      </div>
                      <div className="space-y-1">
                        <p className="text-xs text-dark-400">Failed Checks</p>
                        <p className="text-lg font-medium text-error-400">
                          {accountDetail.recent_scans?.failed_findings || 0}
                        </p>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      className="w-full mt-4"
                      onClick={() =>
                        navigate(
                          `/dashboard/scans/${accountDetail.recent_scans?.id}`
                        )
                      }
                    >
                      View Scan Details
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="py-12 text-center">
                  <p className="text-dark-400">
                    No scans have been run for this account yet.
                  </p>
                  <Button
                    variant="primary"
                    className="mx-auto mt-4"
                    onClick={() => {
                      setIsLaunchingScan(true);
                      setScanModalOpen(true);
                    }}
                    isLoading={isLaunchingScan}
                    disabled={isLaunchingScan}
                  >
                    Run First Scan
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Right column */}
        <div className="space-y-8">
          {/* Account Credentials */}
          <Card>
            <CardHeader>
              <CardTitle>Account Credentials</CardTitle>
              <CardDescription>
                Authentication details for this account
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm text-dark-400 block mb-1">
                  AWS Account ID
                </label>
                <div className="flex items-center bg-dark-900 p-2 rounded-md border border-dark-700">
                  <Database size={16} className="text-dark-500 mr-2" />
                  <code className="font-mono text-dark-300 text-sm">
                    {accountDetail.credential_data.aws_account_id}
                  </code>
                </div>
              </div>

              <div>
                <label className="text-sm text-dark-400 block mb-1">
                  Access Key ID
                </label>
                <div className="flex items-center bg-dark-900 p-2 rounded-md border border-dark-700">
                  <Key size={16} className="text-dark-500 mr-2" />
                  <code className="font-mono text-dark-300 text-sm">
                    {accountDetail.credential_data.access_key}
                  </code>
                </div>
              </div>

              <div>
                <label className="text-sm text-dark-400 block mb-1">
                  Secret Access Key
                </label>
                <div className="flex items-center bg-dark-900 p-2 rounded-md border border-dark-700">
                  <Key size={16} className="text-dark-500 mr-2" />
                  <code className="font-mono text-dark-300 text-sm">
                    {showSecret
                      ? accountDetail.credential_data.secret_key
                      : "••••••••••••••••••••••••••"}
                  </code>
                  <button
                    className="ml-auto text-dark-400 hover:text-dark-200 transition-colors"
                    onClick={() => setShowSecret(!showSecret)}
                    aria-label={
                      showSecret ? "Hide secret key" : "Show secret key"
                    }
                  >
                    {showSecret ? <EyeOff size={16} /> : <Eye size={16} />}
                  </button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        title="Delete Account"
        size="sm"
      >
        <div className="p-6">
          <div className="mb-6">
            <h3 className="text-xl font-semibold text-dark-100 mb-3">
              Are you sure?
            </h3>
            <p className="text-dark-400">
              This will permanently delete the {providerName} account "
              {accountDetail.account_name}" and remove all scan history. This
              action cannot be undone.
            </p>
          </div>

          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => setShowDeleteConfirm(false)}
            >
              Cancel
            </Button>
            <Button variant="error" onClick={handleDeleteConfirm}>
              Delete Account
            </Button>
          </div>
        </div>
      </Modal>

      {/* Scan Modal */}
      <ScanModal
        isOpen={scanModalOpen}
        onClose={() => {
          setScanModalOpen(false);

          // Explicitly invalidate the account details query
          if (accountId) {
            // Also invalidate using the ACCOUNT_QUERY_KEYS constant for consistency
            queryClient.invalidateQueries({
              queryKey: [ACCOUNT_QUERY_KEYS.ACCOUNT_DETAILS, accountId],
            });
          }

          // Refetch account details after the modal is closed
          // This ensures we have the latest data, especially if a scan was started
          refetchAccountDetails();
          setIsLaunchingScan(false);
        }}
        provider={providerName as "AWS" | "GCP" | "Azure"}
        accountId={accountDetail.id}
        providerId={cloudProviderId}
      />
    </div>
  );
};

export default AccountDetails;
