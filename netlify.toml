# Netlify Configuration for CloudAudit Frontend

[build]
  # Build command
  command = "npm run build:production"
  
  # Output directory
  publish = "dist"
  
  # Environment variables for build
  environment = { VITE_APP_ENV = "production", VITE_BUILD_TARGET = "production" }

[build.processing]
  # Skip processing for already optimized files
  skip_processing = false

[build.processing.css]
  # CSS processing
  bundle = true
  minify = true

[build.processing.js]
  # JavaScript processing
  bundle = true
  minify = true

[build.processing.html]
  # HTML processing
  pretty_urls = true

[build.processing.images]
  # Image processing
  compress = true

# Redirects and rewrites
[[redirects]]
  # SPA fallback
  from = "/*"
  to = "/index.html"
  status = 200
  conditions = {Role = ["admin", "user"]}

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    X-Content-Type-Options = "nosniff"
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "camera=(), microphone=(), geolocation=(), payment=()"
    Strict-Transport-Security = "max-age=31536000; includeSubDomains; preload"

[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/logo.svg"
  [headers.values]
    Cache-Control = "public, max-age=604800"

[[headers]]
  for = "/manifest.json"
  [headers.values]
    Cache-Control = "public, max-age=86400"

[[headers]]
  for = "/robots.txt"
  [headers.values]
    Cache-Control = "public, max-age=86400"

# Environment-specific settings
[context.production]
  command = "npm run build:production"
  environment = { VITE_APP_ENV = "production" }

[context.staging]
  command = "npm run build:staging"
  environment = { VITE_APP_ENV = "staging" }

[context.deploy-preview]
  command = "npm run build:staging"
  environment = { VITE_APP_ENV = "staging" }

[context.branch-deploy]
  command = "npm run build:development"
  environment = { VITE_APP_ENV = "development" }

# Plugin configuration
[[plugins]]
  package = "@netlify/plugin-lighthouse"
  
  [plugins.inputs.thresholds]
    performance = 0.9
    accessibility = 0.9
    best-practices = 0.9
    seo = 0.9

[[plugins]]
  package = "netlify-plugin-submit-sitemap"
  
  [plugins.inputs]
    baseUrl = "https://cloudaudit.com"
    sitemapPath = "/sitemap.xml"
    ignorePeriod = 0
    providers = [
      "google",
      "bing"
    ]

# Edge functions (if needed)
[[edge_functions]]
  function = "security-headers"
  path = "/*"

# Forms (if needed for contact forms)
[forms]
  spam_protection = true

# Functions (if needed for serverless functions)
[functions]
  directory = "netlify/functions"
  node_bundler = "esbuild"
