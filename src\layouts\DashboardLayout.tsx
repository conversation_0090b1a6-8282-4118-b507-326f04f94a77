import { motion } from "framer-motion";
import {
  ChevronLeft,
  ChevronRight,
  LayoutDashboard,
  LogOut,
  Menu,
  Scan,
  Shield,
  Users,
  X,
} from "lucide-react";
import React, { useState } from "react";
import { NavLink, Outlet, useLocation, useNavigate } from "react-router-dom";
import Button from "../components/ui/Button";
import { useAuth } from "@/hooks/useAuth";
import Breadcrumbs from "@/components/Breadcrumbs";
import { usePermissionContext } from "@/contexts/PermissionContext";
import { PERMISSIONS } from "@/types/permissions";

const DashboardLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuth();
  const { hasPermission, hasAnyPermission } = usePermissionContext();

  const handleLogout = async () => {
    try {
      await logout.mutateAsync();
    } catch (error) {
      // Error is already handled in the mutation
    }
  };

  // Define navigation items with permission requirements
  const navItems = [
    {
      path: "/dashboard",
      name: "Dashboard",
      icon: <LayoutDashboard size={collapsed ? 22 : 20} />,
      // Dashboard is accessible to all authenticated users
      requiredPermissions: null,
    },
    {
      path: "/dashboard/scans",
      name: "Scans",
      icon: <Scan size={collapsed ? 22 : 20} />,
      // Scans require scan_service permission or read_only
      requiredPermissions: [PERMISSIONS.SCAN_SERVICE, PERMISSIONS.READ_ONLY],
      requireAny: true,
    },
    {
      path: "/dashboard/users",
      name: "Users",
      icon: <Users size={collapsed ? 22 : 20} />,
      // Users require any user management permission
      requiredPermissions: [
        PERMISSIONS.CREATE_USER,
        PERMISSIONS.DELETE_USER,
        PERMISSIONS.UPDATE_USER_PERMISSIONS,
        PERMISSIONS.ASSIGN_ROLE_TO_USER,
        PERMISSIONS.REVOKE_ROLE_FROM_USER,
        PERMISSIONS.CREATE_CUSTOM_ROLE,
        PERMISSIONS.UPDATE_CUSTOM_ROLE,
        PERMISSIONS.DELETE_CUSTOM_ROLE,
      ],
      requireAny: true,
    },
  ];

  // Check if current route is account detail or scan details/findings
  const isDetailPage =
    location.pathname.includes("/dashboard/accounts/") ||
    location.pathname.includes("/dashboard/scans/");

  return (
    <div className="flex h-screen overflow-hidden bg-dark-950">
      {/* Mobile menu overlay */}
      {mobileMenuOpen && (
        <div
          className="fixed inset-0 bg-black/80 z-40 lg:hidden"
          onClick={() => setMobileMenuOpen(false)}
        />
      )}

      {/* Sidebar */}
      <motion.aside
        initial={false}
        animate={{ width: collapsed ? 80 : 300 }}
        transition={{ duration: 0.3, ease: "easeInOut" }}
        className={`
          fixed top-0 bottom-0 lg:relative bg-dark-900 border-r border-dark-800 z-50
          ${mobileMenuOpen ? "left-0" : "-left-full lg:left-0"}
        `}
        style={{ overflow: "visible" }}
      >
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div
            className={`p-5 flex items-center ${
              collapsed ? "justify-center" : "justify-between"
            } border-b border-dark-800`}
          >
            <div className="flex items-center">
              <div className="bg-gradient-to-br from-primary-500 to-secondary-500 p-2 rounded-lg mr-3">
                <Shield size={collapsed ? 20 : 22} className="text-white" />
              </div>
              <motion.h1
                initial={false}
                animate={{
                  opacity: collapsed ? 0 : 1,
                  width: collapsed ? 0 : "auto",
                }}
                transition={{ duration: 0.2 }}
                className="text-lg font-bold text-white overflow-hidden whitespace-nowrap"
              >
                CloudAudit
              </motion.h1>
            </div>

            {!collapsed && (
              <button
                className="lg:hidden text-dark-400 hover:text-white transition-colors"
                onClick={() => setMobileMenuOpen(false)}
              >
                <X size={20} />
              </button>
            )}
          </div>

          {/* User info */}
          <div
            className={`
            p-4 border-b border-dark-800
            ${collapsed ? "flex justify-center" : "block"}
          `}
          >
            <div
              className={`relative group ${
                collapsed ? "" : "flex items-center"
              }`}
            >
              <div
                className={`w-10 h-10 rounded-full bg-primary-500/20 flex items-center justify-center text-primary-400 font-semibold ${
                  collapsed ? "" : "mr-3"
                }`}
              >
                {user?.email?.charAt(0).toUpperCase() || "U"}
              </div>

              {collapsed ? (
                <div className="absolute left-full ml-2 top-1/2 -translate-y-1/2 hidden group-hover:block z-50 min-w-48">
                  <div className="bg-dark-800 text-dark-100 p-3 rounded shadow-lg">
                    <p className="font-medium text-dark-100">
                      {user?.email || "User"}
                    </p>
                    <p className="text-xs text-dark-400">
                      {user?.workspace_name || "Workspace"}
                    </p>
                    {user?.roles && user.roles.length > 0 && (
                      <p className="text-xs text-primary-400 mt-1">
                        {user.roles.map((role) => role.name).join(", ")}
                      </p>
                    )}
                  </div>
                </div>
              ) : (
                <motion.div
                  initial={false}
                  animate={{
                    opacity: collapsed ? 0 : 1,
                    width: collapsed ? 0 : "auto",
                  }}
                  transition={{ duration: 0.2 }}
                  className="overflow-hidden"
                >
                  <p className="font-medium text-dark-100 truncate">
                    {user?.email || "User"}
                  </p>
                  <p className="text-xs text-dark-400 truncate">
                    {user?.workspace_name || "Workspace"}
                  </p>
                  {user?.roles && user.roles.length > 0 && (
                    <p className="text-xs text-primary-400 truncate">
                      {user.roles.map((role) => role.name).join(", ")}
                    </p>
                  )}
                </motion.div>
              )}
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 overflow-y-auto py-4">
            <ul className="space-y-1 px-3">
              {navItems.map((item) => {
                // Check if the user has the required permissions for this nav item
                const hasRequiredPermissions =
                  !item.requiredPermissions || // No permissions required
                  (item.requireAny
                    ? hasAnyPermission(item.requiredPermissions)
                    : item.requiredPermissions.every((perm) =>
                        hasPermission(perm)
                      ));

                // Skip rendering this nav item if the user doesn't have the required permissions
                if (!hasRequiredPermissions) return null;

                return (
                  <li key={item.path} className="relative group">
                    <NavLink
                      to={item.path}
                      end={item.path === "/dashboard"} // Only use exact matching for dashboard
                      className={({ isActive }) => {
                        // Custom isActive logic for specific routes
                        if (item.path === "/dashboard/scans") {
                          isActive =
                            location.pathname === "/dashboard/scans" ||
                            (location.pathname.startsWith(
                              "/dashboard/scans/"
                            ) &&
                              !location.pathname.includes("/findings"));
                        } else if (item.path === "/dashboard") {
                          isActive = location.pathname === "/dashboard";
                        }

                        return `flex items-center p-3 rounded-md transition-all ${
                          isActive
                            ? "bg-primary-500/10 text-primary-400"
                            : "text-dark-400 hover:bg-dark-800 hover:text-dark-200"
                        } ${collapsed ? "justify-center" : ""}`;
                      }}
                    >
                      <span className="flex-shrink-0">{item.icon}</span>
                      <motion.span
                        initial={false}
                        animate={{
                          opacity: collapsed ? 0 : 1,
                          width: collapsed ? 0 : "auto",
                        }}
                        transition={{ duration: 0.2 }}
                        className="ml-3 overflow-hidden whitespace-nowrap"
                      >
                        {item.name}
                      </motion.span>
                    </NavLink>

                    {/* Tooltip for collapsed state */}
                    {collapsed && (
                      <div className="absolute left-full ml-2 top-1/2 -translate-y-1/2 hidden group-hover:block z-50">
                        <div className="bg-dark-800 text-dark-100 text-sm py-1 px-3 rounded shadow-lg whitespace-nowrap">
                          {item.name}
                        </div>
                      </div>
                    )}
                  </li>
                );
              })}
            </ul>
          </nav>

          {/* Sidebar Footer */}
          <div className="p-4 border-t border-dark-800">
            <Button
              variant="outline"
              size="sm"
              leftIcon={<LogOut size={16} />}
              onClick={handleLogout}
              fullWidth={!collapsed}
              className={collapsed ? "justify-center" : ""}
              isLoading={logout.isPending}
            >
              <motion.span
                initial={false}
                animate={{
                  opacity: collapsed ? 0 : 1,
                  width: collapsed ? 0 : "auto",
                }}
                transition={{ duration: 0.2 }}
                className="overflow-hidden whitespace-nowrap"
              >
                Log Out
              </motion.span>
            </Button>
          </div>

          {/* Collapse toggle button with tooltip */}
          <div className="hidden lg:flex justify-center p-3 border-t border-dark-800 relative group">
            <button
              className="p-1.5 rounded-md bg-dark-800 text-dark-400 hover:text-dark-200 transition-colors"
              onClick={() => setCollapsed(!collapsed)}
              aria-label={collapsed ? "Expand sidebar" : "Collapse sidebar"}
            >
              {collapsed ? (
                <ChevronRight size={16} />
              ) : (
                <ChevronLeft size={16} />
              )}
            </button>
            <div className="absolute bottom-full mb-2 hidden group-hover:block">
              <div className="bg-dark-800 text-dark-200 text-xs py-1 px-2 rounded">
                {collapsed ? "Expand sidebar" : "Collapse sidebar"}
              </div>
            </div>
          </div>
        </div>
      </motion.aside>

      {/* Main content */}
      <main className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="bg-dark-900 border-b border-dark-800 p-4 flex items-center justify-between">
          <div className="flex items-center">
            <button
              className="lg:hidden mr-4 text-dark-400 hover:text-dark-200"
              onClick={() => setMobileMenuOpen(true)}
            >
              <Menu size={20} />
            </button>

            {isDetailPage && (
              <button
                className="flex items-center text-dark-400 hover:text-dark-200 mr-4"
                onClick={() => navigate(-1)}
              >
                <ChevronLeft size={16} className="mr-1" />
                <span>Back</span>
              </button>
            )}

            <h1 className="text-xl font-semibold text-dark-100">
              {location.pathname.includes("/dashboard/accounts/")
                ? "Account Details"
                : location.pathname.includes("/dashboard/scans/") &&
                  location.pathname.includes("/findings/")
                ? "Finding Details"
                : location.pathname.includes("/dashboard/scans/") &&
                  location.pathname.includes("/findings")
                ? "Scan Findings"
                : location.pathname.includes("/dashboard/scans/")
                ? "Scan Details"
                : location.pathname === "/dashboard/scans"
                ? "Scans"
                : location.pathname === "/dashboard"
                ? "Dashboard"
                : location.pathname === "/dashboard/users"
                ? "Users"
                : "Dashboard"}
            </h1>
          </div>
        </header>

        {/* Page content */}
        <div className="flex-1 overflow-auto bg-dark-950 p-6">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            {/* Show breadcrumbs only on detail pages, but not on findings pages */}
            {isDetailPage && !location.pathname.includes("/findings") && (
              <Breadcrumbs />
            )}
            <Outlet />
          </motion.div>
        </div>
      </main>
    </div>
  );
};

export default DashboardLayout;
