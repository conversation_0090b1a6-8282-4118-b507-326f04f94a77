/**
 * Production-ready Logger
 * Centralized logging system with multiple output targets
 */

import { config } from './config';

// Log levels
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

// Log entry interface
export interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: string;
  data?: any;
  userId?: string;
  sessionId?: string;
  url?: string;
  userAgent?: string;
  stack?: string;
}

// Logger class
class Logger {
  private logLevel: LogLevel;
  private enableConsole: boolean;
  private enableRemote: boolean;
  private remoteEndpoint?: string;
  private sessionId: string;

  constructor() {
    // Set log level based on configuration
    this.logLevel = this.getLogLevelFromString(config.logging.level);
    this.enableConsole = config.logging.enableConsoleLogging;
    this.enableRemote = config.logging.enableRemoteLogging;
    this.remoteEndpoint = config.logging.remoteLogEndpoint;
    
    // Generate session ID
    this.sessionId = this.generateSessionId();
  }

  private getLogLevelFromString(level: string): LogLevel {
    switch (level.toLowerCase()) {
      case 'debug': return LogLevel.DEBUG;
      case 'info': return LogLevel.INFO;
      case 'warn': return LogLevel.WARN;
      case 'error': return LogLevel.ERROR;
      default: return LogLevel.INFO;
    }
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.logLevel;
  }

  private createLogEntry(level: LogLevel, message: string, data?: any, stack?: string): LogEntry {
    return {
      level,
      message,
      timestamp: new Date().toISOString(),
      data,
      userId: localStorage.getItem('user_email') || undefined,
      sessionId: this.sessionId,
      url: window.location.href,
      userAgent: navigator.userAgent,
      stack,
    };
  }

  private async logToConsole(entry: LogEntry): Promise<void> {
    if (!this.enableConsole) return;

    const logMessage = `[${entry.timestamp}] ${LogLevel[entry.level]}: ${entry.message}`;
    
    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(logMessage, entry.data);
        break;
      case LogLevel.INFO:
        console.info(logMessage, entry.data);
        break;
      case LogLevel.WARN:
        console.warn(logMessage, entry.data);
        break;
      case LogLevel.ERROR:
        console.error(logMessage, entry.data);
        if (entry.stack) {
          console.error('Stack trace:', entry.stack);
        }
        break;
    }
  }

  private async logToRemote(entry: LogEntry): Promise<void> {
    if (!this.enableRemote || !this.remoteEndpoint) return;

    try {
      await fetch(this.remoteEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          level: LogLevel[entry.level].toLowerCase(),
          message: entry.message,
          timestamp: entry.timestamp,
          data: entry.data,
          metadata: {
            userId: entry.userId,
            sessionId: entry.sessionId,
            url: entry.url,
            userAgent: entry.userAgent,
            appVersion: config.app.version,
            environment: config.app.environment,
          },
          stack: entry.stack,
        }),
      });
    } catch (error) {
      // Fallback to console if remote logging fails
      console.error('Failed to send log to remote endpoint:', error);
      console.error('Original log entry:', entry);
    }
  }

  private async log(level: LogLevel, message: string, data?: any, stack?: string): Promise<void> {
    if (!this.shouldLog(level)) return;

    const entry = this.createLogEntry(level, message, data, stack);

    // Log to console
    await this.logToConsole(entry);

    // Log to remote endpoint
    await this.logToRemote(entry);
  }

  // Public logging methods
  public debug(message: string, data?: any): void {
    this.log(LogLevel.DEBUG, message, data);
  }

  public info(message: string, data?: any): void {
    this.log(LogLevel.INFO, message, data);
  }

  public warn(message: string, data?: any): void {
    this.log(LogLevel.WARN, message, data);
  }

  public error(message: string, error?: Error | any, data?: any): void {
    let stack: string | undefined;
    let errorData = data;

    if (error instanceof Error) {
      stack = error.stack;
      errorData = {
        ...data,
        errorName: error.name,
        errorMessage: error.message,
      };
    } else if (error) {
      errorData = {
        ...data,
        error,
      };
    }

    this.log(LogLevel.ERROR, message, errorData, stack);
  }

  // Performance logging
  public performance(operation: string, duration: number, data?: any): void {
    this.info(`Performance: ${operation}`, {
      duration,
      ...data,
    });
  }

  // API logging
  public apiCall(method: string, url: string, duration: number, status: number, data?: any): void {
    const level = status >= 400 ? LogLevel.ERROR : LogLevel.INFO;
    this.log(level, `API Call: ${method} ${url}`, {
      method,
      url,
      duration,
      status,
      ...data,
    });
  }

  // User action logging
  public userAction(action: string, data?: any): void {
    this.info(`User Action: ${action}`, data);
  }

  // Security logging
  public security(event: string, data?: any): void {
    this.warn(`Security Event: ${event}`, data);
  }

  // Business logic logging
  public business(event: string, data?: any): void {
    this.info(`Business Event: ${event}`, data);
  }

  // Set log level dynamically
  public setLogLevel(level: LogLevel): void {
    this.logLevel = level;
  }

  // Get current log level
  public getLogLevel(): LogLevel {
    return this.logLevel;
  }

  // Enable/disable console logging
  public setConsoleLogging(enabled: boolean): void {
    this.enableConsole = enabled;
  }

  // Enable/disable remote logging
  public setRemoteLogging(enabled: boolean): void {
    this.enableRemote = enabled;
  }

  // Set remote endpoint
  public setRemoteEndpoint(endpoint: string): void {
    this.remoteEndpoint = endpoint;
    this.enableRemote = true;
  }

  // Flush logs (for cleanup on page unload)
  public async flush(): Promise<void> {
    // Implementation for flushing any pending logs
    // This could be useful for batched logging
  }
}

// Create singleton logger instance
const logger = new Logger();

// Export logger instance and types
export { logger, Logger };
export default logger;
