import type {
    AccountDetailResponse,
    AccountsResponse,
    CloudProvidersResponse,
    CloudServiceResponse
} from '@/types/api';
import { apiClient } from './baseService';

/**
 * Get all cloud providers
 * @returns Promise with cloud providers response
 */
export const getCloudProviders = async (): Promise<CloudProvidersResponse> => {
    const response = await apiClient.get<CloudProvidersResponse>('/api/cloud-providers');
    return response.data;
};

/**
 * Get services for a specific cloud provider
 * @param cloudProviderId The cloud provider ID
 * @returns Promise with services response
 */
export const getServices = async (cloudProviderId: number): Promise<CloudServiceResponse> => {
    const response = await apiClient.get<CloudServiceResponse>('/api/services', {
        params: { cloud_provider_id: cloudProviderId }
    });
    return response.data;
};

/**
 * Fetch accounts for all providers or a specific provider
 * @param providerId Optional provider ID to filter accounts
 * @returns Promise with accounts response
 */
export const fetchAccounts = async (providerId?: number): Promise<AccountsResponse[]> => {
    // If providerId is specified, fetch accounts for that provider only
    if (providerId) {
        const response = await apiClient.get<AccountsResponse>('/api/accounts', {
            params: { cloud_provider_id: providerId }
        });
        return [response.data];
    }

    // Otherwise fetch accounts for all enabled providers
    try {
        // First fetch all cloud providers to get only enabled ones
        const providersResponse = await getCloudProviders();
        const enabledProviders = providersResponse.data.cloud_providers
            .filter(provider => provider.is_enable)
            .map(provider => provider.id);

        // If no enabled providers, return empty array
        if (enabledProviders.length === 0) {
            return [];
        }

        // Fetch accounts only for enabled providers
        const responses = await Promise.all(
            enabledProviders.map(id =>
                apiClient.get<AccountsResponse>('/api/accounts', {
                    params: { cloud_provider_id: id }
                })
            )
        );
        return responses.map(response => response.data);
    } catch (error) {
        console.error('Error fetching cloud providers:', error);
        // In case of error, return empty array to prevent app crash
        return [];
    }
};

/**
 * Get detailed information for a specific account
 * @param accountId The account ID
 * @returns Promise with account detail response
 */
export const getAccountDetails = async (accountId: number): Promise<AccountDetailResponse> => {
    const response = await apiClient.get<AccountDetailResponse>(`/api/accounts/${accountId}`);
    return response.data;
};
