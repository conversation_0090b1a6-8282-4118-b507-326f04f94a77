import { CloudService } from '@/types/api';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

/**
 * Cloud provider name type
 * Used throughout the application for provider identification
 */
export type CloudProviderName = "AWS" | "GCP" | "Azure";

/**
 * Cloud provider interface
 * Represents a cloud provider with its enabled/disabled state
 */
export interface CloudProvider {
    id: number;
    name: CloudProviderName;
    is_enable: boolean;
}

/**
 * Provider store state interface
 * Contains only the necessary state and methods for cloud provider functionality
 */
interface ProviderState {
    // Services state
    services: CloudService[];
    setServices: (services: CloudService[]) => void;
    clearServices: () => void;

    // Provider mapping state
    providerMap: Record<CloudProviderName, number>;
    idToNameMap: Record<number, CloudProviderName>;
    setProviderMaps: (providers: CloudProvider[]) => void;

    // Provider utility functions
    getProviderId: (provider: CloudProviderName | null) => number | undefined;
    getProviderFromId: (id: number) => CloudProviderName | undefined;
}

/**
 * Provider store for managing cloud provider services and utility functions
 */
export const useProviderStore = create<ProviderState>()(
    persist(
        (set, get) => ({
            // Services state
            services: [],
            setServices: (services) => set({ services }),
            clearServices: () => set({ services: [] }),

            // Provider mapping state - initialized with defaults but will be updated with API data
            providerMap: {
                AWS: 1,
                GCP: 2,
                Azure: 3,
            },
            idToNameMap: {
                1: "AWS",
                2: "GCP",
                3: "Azure",
            },
            setProviderMaps: (providers) => {
                // Start with default mappings
                const providerMap: Record<CloudProviderName, number> = {
                    AWS: 1,
                    GCP: 2,
                    Azure: 3,
                };
                const idToNameMap: Record<number, CloudProviderName> = {
                    1: "AWS",
                    2: "GCP",
                    3: "Azure",
                };

                // Update with actual provider data from API
                providers.forEach(provider => {
                    providerMap[provider.name] = provider.id;
                    idToNameMap[provider.id] = provider.name;
                });

                set({ providerMap, idToNameMap });
            },

            // Provider utility functions
            getProviderId: (provider) => {
                if (!provider) return undefined;
                return get().providerMap[provider];
            },

            getProviderFromId: (id) => {
                return get().idToNameMap[id];
            },
        }),
        {
            name: 'provider-storage'
        }
    )
);