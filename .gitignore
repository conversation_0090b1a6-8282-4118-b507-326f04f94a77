# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/
jspm_packages/

# Build outputs
dist/
dist-ssr/
build/
out/

# Environment files
.env
.env.local
.env.*.local
.env.test

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
Thumbs.db
ehthumbs.db
Desktop.ini

# Runtime files
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Docker
.dockerignore

# SSL certificates
ssl/
*.pem
*.key
*.crt

# Lighthouse reports
lighthouse-results/
lhci_reports/

# Bundle analyzer reports
stats.html
bundle-analyzer-report.html

# Vercel
.vercel

# Netlify
.netlify

# Local development
.local

# Sentry
.sentryclirc

# Terraform
*.tfstate
*.tfstate.*
.terraform/

# AWS
.aws/

# Azure
.azure/

# Google Cloud
.gcloud/
.env.development.local
.env.test.local
.env.production.local

# Testing
/coverage

# Production build
/build