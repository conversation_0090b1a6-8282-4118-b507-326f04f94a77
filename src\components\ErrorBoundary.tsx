import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Home, RefreshCw } from "lucide-react";
import { Component, ErrorInfo, ReactNode } from "react";

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string | null;
}

class ErrorBoundary extends Component<Props, State> {
  private retryCount = 0;
  private maxRetries = 3;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random()
        .toString(36)
        .substring(2, 11)}`,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error to console in development
    if (import.meta.env.VITE_APP_ENV === "development") {
      console.error("ErrorBoundary caught an error:", error, errorInfo);
    }

    // Update state with error info
    this.setState({
      error,
      errorInfo,
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Report error to monitoring service if enabled
    this.reportError(error, errorInfo);
  }

  private reportError = async (error: Error, errorInfo: ErrorInfo) => {
    try {
      // Only report errors in production or staging
      if (import.meta.env.VITE_ENABLE_ERROR_REPORTING !== "true") {
        return;
      }

      const errorReport = {
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        errorId: this.state.errorId,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        userId: localStorage.getItem("user_email") || "anonymous",
        appVersion: import.meta.env.VITE_APP_VERSION || "unknown",
        environment: import.meta.env.VITE_APP_ENV || "unknown",
      };

      // Send to remote logging endpoint if configured
      const logEndpoint = import.meta.env.VITE_REMOTE_LOG_ENDPOINT;
      if (logEndpoint) {
        await fetch(logEndpoint, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            level: "error",
            message: "React Error Boundary",
            data: errorReport,
          }),
        });
      }

      // Send to Sentry if configured
      const sentryDsn = import.meta.env.VITE_SENTRY_DSN;
      if (sentryDsn) {
        try {
          // Dynamically import Sentry to avoid bundling it when not needed
          import("@sentry/react")
            .then((Sentry) => {
              Sentry.captureException(error, {
                contexts: {
                  react: {
                    componentStack: errorInfo.componentStack,
                  },
                },
                tags: {
                  errorBoundary: true,
                  errorId: this.state.errorId,
                },
              });
            })
            .catch(() => {
              // Silently fail if Sentry is not available
              console.warn("Failed to report error to Sentry");
            });
        } catch {
          // Fallback: try to use window.Sentry if available
          const windowSentry = (window as any).Sentry;
          if (windowSentry?.captureException) {
            windowSentry.captureException(error, {
              contexts: {
                react: {
                  componentStack: errorInfo.componentStack,
                },
              },
              tags: {
                errorBoundary: true,
                errorId: this.state.errorId,
              },
            });
          }
        }
      }
    } catch (reportingError) {
      console.error("Failed to report error:", reportingError);
    }
  };

  private handleRetry = () => {
    if (this.retryCount < this.maxRetries) {
      this.retryCount++;
      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        errorId: null,
      });
    }
  };

  private handleReload = () => {
    window.location.reload();
  };

  private handleGoHome = () => {
    window.location.href = "/";
  };

  private copyErrorDetails = () => {
    const errorDetails = {
      errorId: this.state.errorId,
      message: this.state.error?.message,
      stack: this.state.error?.stack,
      componentStack: this.state.errorInfo?.componentStack,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
    };

    navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2));
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <div className="min-h-screen bg-dark-950 flex items-center justify-center p-4">
          <div className="max-w-md w-full bg-dark-800 rounded-lg shadow-xl p-6 text-center">
            <div className="mb-6">
              <AlertTriangle className="w-16 h-16 text-error-500 mx-auto mb-4" />
              <h1 className="text-2xl font-bold text-dark-100 mb-2">
                Oops! Something went wrong
              </h1>
              <p className="text-dark-300 mb-4">
                We're sorry, but something unexpected happened. Our team has
                been notified.
              </p>

              {import.meta.env.VITE_APP_ENV === "development" &&
                this.state.error && (
                  <div className="bg-dark-900 rounded p-3 mb-4 text-left">
                    <p className="text-error-400 text-sm font-mono break-all">
                      {this.state.error.message}
                    </p>
                  </div>
                )}

              {this.state.errorId && (
                <p className="text-dark-400 text-sm mb-4">
                  Error ID:{" "}
                  <span className="font-mono">{this.state.errorId}</span>
                </p>
              )}
            </div>

            <div className="space-y-3">
              {this.retryCount < this.maxRetries && (
                <button
                  onClick={this.handleRetry}
                  className="w-full bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2"
                >
                  <RefreshCw className="w-4 h-4" />
                  Try Again ({this.maxRetries - this.retryCount} attempts left)
                </button>
              )}

              <button
                onClick={this.handleReload}
                className="w-full bg-dark-700 hover:bg-dark-600 text-dark-100 font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2"
              >
                <RefreshCw className="w-4 h-4" />
                Reload Page
              </button>

              <button
                onClick={this.handleGoHome}
                className="w-full bg-dark-700 hover:bg-dark-600 text-dark-100 font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2"
              >
                <Home className="w-4 h-4" />
                Go to Homepage
              </button>

              {import.meta.env.VITE_APP_ENV === "development" && (
                <button
                  onClick={this.copyErrorDetails}
                  className="w-full bg-dark-700 hover:bg-dark-600 text-dark-100 font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2"
                >
                  <Bug className="w-4 h-4" />
                  Copy Error Details
                </button>
              )}
            </div>

            <div className="mt-6 pt-4 border-t border-dark-700">
              <p className="text-dark-400 text-xs">
                If this problem persists, please contact support with the error
                ID above.
              </p>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
