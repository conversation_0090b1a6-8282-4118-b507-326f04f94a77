# CloudAudit Frontend - Robots.txt
# This file tells search engines which pages they can and cannot crawl

# For production environment
User-agent: *
Disallow: /api/
Disallow: /admin/
Disallow: /dashboard/
Disallow: /login
Disallow: /signup
Disallow: /reset-password
Disallow: /verify-email
Disallow: /_next/
Disallow: /assets/
Disallow: /static/

# Allow access to public pages
Allow: /
Allow: /about
Allow: /contact
Allow: /privacy
Allow: /terms

# Sitemap location
Sitemap: https://cloudaudit.com/sitemap.xml

# Crawl delay (optional)
Crawl-delay: 1
