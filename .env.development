# CloudAudit Frontend - Development Environment
# This file contains development-specific environment variables

# =============================================================================
# API Configuration
# =============================================================================
VITE_API_BASE_URL=http://192.168.2.112:8000
VITE_API_TIMEOUT=30000
VITE_API_MAX_RETRIES=3

# =============================================================================
# Application Configuration
# =============================================================================
VITE_APP_ENV=development
VITE_APP_NAME=CloudAudit
VITE_APP_VERSION=1.0.0-dev
VITE_APP_BASE_URL=/

# =============================================================================
# Security Configuration
# =============================================================================
VITE_ENABLE_SECURITY_HEADERS=false
VITE_CSP_MODE=report-only
VITE_FORCE_HTTPS=false
VITE_SESSION_TIMEOUT=120

# =============================================================================
# Feature Flags
# =============================================================================
VITE_ENABLE_REACT_QUERY_DEVTOOLS=true
VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_ENABLE_ERROR_REPORTING=false
VITE_ENABLE_ANALYTICS=false

# =============================================================================
# Logging Configuration
# =============================================================================
VITE_LOG_LEVEL=debug
VITE_ENABLE_CONSOLE_LOGGING=true
VITE_ENABLE_REMOTE_LOGGING=false

# =============================================================================
# Performance Configuration
# =============================================================================
VITE_ENABLE_SERVICE_WORKER=false
VITE_CACHE_STRATEGY=network-first
VITE_CACHE_DURATION=300

# =============================================================================
# Build Configuration
# =============================================================================
VITE_BUILD_TARGET=development
VITE_ENABLE_SOURCE_MAPS=true
VITE_ENABLE_BUNDLE_ANALYZER=false

# =============================================================================
# Development Configuration
# =============================================================================
VITE_DEV_PORT=5173
VITE_DEV_HOST=0.0.0.0
VITE_ENABLE_HMR=true
VITE_DEV_HTTPS=false

# =============================================================================
# Monitoring & Observability
# =============================================================================
VITE_HEALTH_CHECK_ENDPOINT=/health
VITE_METRICS_INTERVAL=30
VITE_ENABLE_RUM=false
