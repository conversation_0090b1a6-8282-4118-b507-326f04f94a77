# CloudAudit Frontend - Production-Ready React Application

A comprehensive cloud security scanning and compliance monitoring platform built with <PERSON>act, TypeScript, and Vite. This application provides a modern, secure, and performant frontend for cloud security auditing.

## 🚀 Features

- **Modern Tech Stack**: React 18, TypeScript, Vite 6, TailwindCSS
- **Production-Ready**: Optimized builds, security headers, error boundaries
- **Multi-Environment**: Development, staging, and production configurations
- **Security-First**: CSP headers, XSS protection, secure defaults
- **Performance Optimized**: Code splitting, lazy loading, caching strategies
- **Error Handling**: Comprehensive error boundaries and logging
- **Monitoring**: Performance monitoring and error reporting
- **PWA Ready**: Service worker support and web app manifest

## 📋 Prerequisites

- [Node.js](https://nodejs.org/) (v20.x or later)
- [npm](https://www.npmjs.com/) (latest version recommended)
- [Git](https://git-scm.com/downloads)
- [Docker](https://docs.docker.com/get-docker/) (optional, for containerized deployment)

## 🛠️ Installation

### 1. Clone the repository

```bash
git clone https://github.com/Maruti-Techlabs/cloudaudit-frontend.git
cd cloudaudit-frontend
```

### 2. Install dependencies

```bash
npm install
```

### 3. Environment Configuration

Copy the environment template and configure for your environment:

```bash
# For development
cp .env.example .env.development

# For staging
cp .env.example .env.staging

# For production
cp .env.example .env.production
```

Edit the environment files with your specific configuration values.

## 🏃‍♂️ Development

### Start development server

```bash
npm run dev
# or for staging environment
npm run dev:staging
```

### Available Scripts

- `npm run dev` - Start development server
- `npm run dev:staging` - Start development server with staging config
- `npm run build` - Build for production
- `npm run build:development` - Build for development
- `npm run build:staging` - Build for staging
- `npm run build:production` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues
- `npm run type-check` - Run TypeScript type checking
- `npm run validate` - Run type checking and linting

### 4. Access the application

Open your browser and navigate to:

- **Development**: http://localhost:5173
- **Preview**: http://localhost:4173

## 🚀 Production Deployment

### Environment Variables

Configure the following environment variables for production:

```bash
# API Configuration
VITE_API_BASE_URL=https://api.cloudaudit.com
VITE_API_TIMEOUT=30000
VITE_API_MAX_RETRIES=3

# Application Configuration
VITE_APP_ENV=production
VITE_APP_NAME=CloudAudit
VITE_APP_VERSION=1.0.0

# Security Configuration
VITE_ENABLE_SECURITY_HEADERS=true
VITE_CSP_MODE=enforce
VITE_FORCE_HTTPS=true

# Feature Flags
VITE_ENABLE_ERROR_REPORTING=true
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_PERFORMANCE_MONITORING=true

# Third-party Services
VITE_SENTRY_DSN=your-sentry-dsn
VITE_GA_TRACKING_ID=your-ga-id
```

### Build for Production

```bash
npm run build:production
```

### Deployment Options

#### 1. Vercel Deployment

```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel --prod
```

#### 2. Netlify Deployment

```bash
# Install Netlify CLI
npm i -g netlify-cli

# Deploy
netlify deploy --prod --dir=dist
```

#### 3. Docker Deployment

```bash
# Build production image
docker build -t cloudaudit-frontend:latest .

# Run container
docker run -p 80:80 cloudaudit-frontend:latest

# Or use docker-compose
docker-compose --profile production up -d
```

#### 4. AWS S3 + CloudFront

```bash
# Build for production
npm run build:production

# Upload to S3 (configure AWS CLI first)
aws s3 sync dist/ s3://your-bucket-name --delete

# Invalidate CloudFront cache
aws cloudfront create-invalidation --distribution-id YOUR_DISTRIBUTION_ID --paths "/*"
```

## 🐳 Docker

### Development with Docker

```bash
# Start development environment
docker-compose --profile development up

# Access at http://localhost:5173
```

### Production with Docker

```bash
# Start production environment
docker-compose --profile production up -d

# Access at http://localhost:80
```

### Staging with Docker

```bash
# Start staging environment
docker-compose --profile staging up -d

# Access at http://localhost:8080
```

## 🔧 Configuration

### Environment Files

- `.env.development` - Development configuration
- `.env.staging` - Staging configuration
- `.env.production` - Production configuration
- `.env.example` - Template with all available options

### Vite Configuration

The `vite.config.ts` file includes:

- **Environment-specific builds**
- **Code splitting and optimization**
- **Asset optimization**
- **Security configurations**
- **Performance optimizations**

### Security Headers

Security headers are configured in:

- `public/_headers` - For Netlify/Vercel
- `nginx-default.conf` - For Docker/Nginx deployments
- `vercel.json` - For Vercel deployments
- `netlify.toml` - For Netlify deployments

## 📊 Performance

### Bundle Analysis

```bash
npm run build:analyze
```

### Performance Monitoring

The application includes:

- **Core Web Vitals monitoring**
- **Performance API integration**
- **Bundle size optimization**
- **Lazy loading and code splitting**
- **Efficient caching strategies**

## 🛡️ Security

### Security Features

- **Content Security Policy (CSP)**
- **XSS Protection**
- **Clickjacking protection**
- **HTTPS enforcement**
- **Secure headers**
- **Input validation**
- **Error boundary protection**

### Security Headers

```
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
Referrer-Policy: strict-origin-when-cross-origin
Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
```

## 📝 Logging and Monitoring

### Error Reporting

Configure Sentry for error reporting:

```bash
VITE_SENTRY_DSN=your-sentry-dsn
VITE_ENABLE_ERROR_REPORTING=true
```

### Performance Monitoring

Enable performance monitoring:

```bash
VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_ENABLE_RUM=true
```

### Logging Levels

- `debug` - Development debugging
- `info` - General information
- `warn` - Warning messages
- `error` - Error messages only

## 🧪 Testing

```bash
# Run tests (when implemented)
npm test

# Run tests with coverage
npm run test:coverage
```

## 📱 PWA Features

The application includes PWA capabilities:

- **Web App Manifest**
- **Service Worker support**
- **Offline functionality**
- **App-like experience**
- **Install prompts**

## 🔄 CI/CD

### GitHub Actions

Create `.github/workflows/deploy.yml`:

```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: "20"
      - run: npm ci
      - run: npm run validate
      - run: npm run build:production
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
          vercel-args: "--prod"
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:

- Create an issue in the repository
- Contact the development team
- Check the documentation

---

**Built with ❤️ by the CloudAudit Team**
